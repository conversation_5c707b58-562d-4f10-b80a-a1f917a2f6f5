import React, { useEffect, useState } from 'react';
import Header from './Header';
import Gallery from './Gallery';
import Footer from './Footer';
import { API_BASE_URL } from '../config';

const FavoriteTitle: React.FC = () => (
  <div className="container mx-auto px-4 pt-12 pb-4 mb-10">
    <h1 className="text-5xl font-bold text-gray-900 mb-2 py-5">My Favorite</h1>
    <div className="border-b  border-gray-300 w-full" />
  </div>
);

const Favorite: React.FC = () => {
  const [likedItems, setLikedItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const getUrlProlfil = (path : string)  => {
            return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
      };

  useEffect(() => {
    const fetchLikedProfiles = async () => {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        setLikedItems([]);
        setLoading(false);
        return;
      }
      try {
        // On récupère tous les professionnels
        const res = await fetch(`${API_BASE_URL}/api/professionals`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!res.ok) throw new Error('Erreur lors de la récupération des professionnels');
        const data = await res.json();
        // On filtre ceux que l'utilisateur a liké
        const professionals = data.professionals || [];
        // Pour chaque professionnel, on vérifie le statut du like
        const liked = await Promise.all(
          professionals.map(async (pro: any) => {
            try {
              const statusRes = await fetch(`${API_BASE_URL}/api/professionals/${pro.id}/like/status`, {
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${token}`,
                },
              });
              if (!statusRes.ok) return null;
              const statusData = await statusRes.json();
              if (statusData.success && statusData.data.liked) {
                return pro;
              }
              return null;
            } catch {
              return null;
            }
          })
        );
        setLikedItems(liked.filter(Boolean));
      } catch (e) {
        setLikedItems([]);
      } finally {
        setLoading(false);
      }
    };
    fetchLikedProfiles();
  }, []);

  // Retirer un profil de la liste après un dislike
  const handleDislike = (id: number) => {
    setLikedItems(prev => prev.filter(item => item.id !== id));
  };

  // Adapter les données pour Gallery
  const galleryItems = likedItems.map((pro: any) => ({
    id: pro.id,
    title: pro.title || `${pro.first_name} ${pro.last_name}`,
    author: `${pro.first_name} ${pro.last_name}`,
    authorAvatar: getUrlProlfil(pro.avatar) || getUrlProlfil(pro.profile_picture_path) || 'https://randomuser.me/api/portraits/men/32.jpg',
    isPro: pro.is_professional ?? true,
    likes: pro.likes_count || 0,
    views: pro.views_count ? String(pro.views_count) : '0',
    image: pro.avatar || pro.profile_picture_path || 'https://randomuser.me/api/portraits/men/32.jpg',
  }));

  return (
    <>
      <Header />
      <div className="w-full text-center my-8">
        <h2
          style={{
            fontSize: '3rem',
            fontFamily: 'Arial, sans-serif',
            letterSpacing: '0.06em',
            paddingTop: '32px',
            paddingBottom: '32px',
            fontWeight: 600,
            lineHeight: 1.1,
            color: '#222',
          }}
        >
          Save your favorite<br />3d Artiste for later
        </h2>
      </div>
      <div className="w-full max-w-[1512px] mx-auto px-4 md:px-20">
        <FavoriteTitle />
        {loading ? (
          <div className="text-center py-20 text-gray-500">Chargement...</div>
        ) : (
          <Gallery items={galleryItems} marginBottom={350} onDislike={handleDislike} />
        )}
      </div>
      <Footer />
    </>
  );
};

export default Favorite; 