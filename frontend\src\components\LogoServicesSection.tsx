
import { useState } from 'react';
import { Button } from './ui/buttons';
import { Badge } from './ui/badges';
import { Filter, ToggleRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

type ServicesProps = {
  services?: any[];
  query?:string;
};
const LogoServicesSection: React.FC<ServicesProps>  = ({services,query}) => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState('Landing Page');
  const [quickHire, setQuickHire] = useState(false);

  const filters = [
    'Landing Page', 'Dashboard', 'Logo Design', 'Illustration', 
    'Mobile App', 'Animation', 'Icons', 'Ecommerce'
  ];

  const logoServices = [
    {
      id: 1,
      title: 'Logo Design Creation Sketch To Vector',
      author: 'Deep Blueocean',
      isPro: true,
      price: '$1,600',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gray-900'
    },
    {
      id: 2,
      title: 'Full Visual Identity',
      author: '<PERSON><PERSON><PERSON>',
      isPro: true,
      price: 'From $10,000',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-green-100 to-green-200'
    },
    {
      id: 3,
      title: 'Branding – Small Package',
      author: 'Creative Studio',
      isPro: true,
      price: '$4,000',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-purple-600 to-blue-800'
    },
    {
      id: 4,
      title: 'Visual Identity Package',
      author: 'Marta Worth Branding Agency',
      isPro: true,
      price: '$3,400',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-gray-200 to-gray-300'
    },
    {
      id: 5,
      title: 'Logo Design, Basic Package',
      author: 'Alex Tass, logo designer',
      isPro: true,
      price: '$1,800',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-white'
    },
    {
      id: 6,
      title: 'Retro Lettering Logotype',
      author: 'FortifiedLetters',
      isPro: true,
      price: 'From $1,000',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gray-50'
    },
    {
      id: 7,
      title: 'Logo Design Creation',
      author: 'Neels',
      isPro: true,
      price: '$2,500',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-green-400 to-green-600'
    },
    {
      id: 8,
      title: 'Essential Logo Design Package',
      author: 'Lucian Peak',
      isPro: true,
      price: 'From $2,800',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gray-100'
    },
    {
      id: 9,
      title: 'Premium Logo / Brand Mark Design',
      author: 'Gert van Duinen (Crash Design)',
      isPro: true,
      price: '$5,000',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gray-900'
    },
    {
      id: 10,
      title: 'Brand Identity',
      author: 'Minat Designers',
      isPro: true,
      price: 'From $6,000',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-red-500 to-pink-600'
    },
    {
      id: 11,
      title: 'Set Of Vector Logo Options',
      author: 'Diego Blancmarch',
      isPro: true,
      price: '$800',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gray-900'
    },
    {
      id: 12,
      title: 'Logo Design',
      author: 'Sara Steele',
      isPro: true,
      price: '$1,500',
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png',
      bgColor: 'bg-gradient-to-br from-green-400 to-teal-500'
    }
  ];

  return (
    <section className="bg-gradient-to-b from-gray-50 to-white pt-20 pb-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">{query} Services</h1>
          <p className="text-lg text-gray-600">{services?.length||0} results</p>
        </div>

        {/* Logo Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {services && services.map((service) => (
            <div key={service.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              
              <div
                className="relative h-48 flex items-center justify-center p-6 rounded-lg overflow-hidden"
                style={{
                  backgroundImage: `url(${service.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop"})`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                {/* Overlay sombre + flou */}
                {/* <div className="absolute inset-0 bg-black/30 backdrop-blur-sm z-0" /> */}

                {/* Contenu par-dessus */}
                {/* <div className="relative z-10 text-white text-xl font-bold opacity-90">
                  {service.title || "Service"}
                </div> */}
              </div>

              {/* <div className={`bg-gradient-to-br from-purple-600 to-blue-800 h-48 flex items-center justify-center p-6`}>
                <div className="w-full h-full bg-white/10 rounded-lg backdrop-blur-sm flex items-center justify-center overflow-hidden">
                  <img
                    src={service.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}
                    alt={service.title}
                    className="object-contain max-h-full max-w-full"
                    onError={(e) => {
                      e.currentTarget.onerror = null; 
                      e.currentTarget.src = 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                    }}
                  />
                </div>
              </div> */}
              
              {/* Service Details */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{service.title}</h3>
                
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">{service.professional_name.charAt(0)}</span>
                  </div>
                  <span className="text-sm text-gray-600">{service.professional_name}</span>
                  {/* {service.isPro && ( */}
                    <Badge className="bg-orange-500 text-white text-xs px-2 py-1">
                      PRO
                    </Badge>
                  {/* )} */}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="font-bold text-gray-900">€{service.price}</span>
                  <Button size="sm" className="bg-black text-white hover:bg-gray-800"
                    onClick={() => {
                      navigate('/details-search', {
                        state: { service }
                      });
                }}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LogoServicesSection;
