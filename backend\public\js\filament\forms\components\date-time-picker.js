var Xn=Object.create;var Zt=Object.defineProperty;var Qn=Object.getOwnPropertyDescriptor;var ei=Object.getOwnPropertyNames;var ti=Object.getPrototypeOf,ni=Object.prototype.hasOwnProperty;var H=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports);var ii=(n,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of ei(t))!ni.call(n,e)&&e!==r&&Zt(n,e,{get:()=>t[e],enumerable:!(i=Qn(t,e))||i.enumerable});return n};var _e=(n,t,r)=>(r=n!=null?Xn(ti(n)):{},ii(t||!n||!n.__esModule?Zt(r,"default",{value:n,enumerable:!0}):r,n));var un=H((ge,Se)=>{(function(n,t){typeof ge=="object"&&typeof Se<"u"?Se.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_plugin_customParseFormat=t()})(ge,function(){"use strict";var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},t=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,r=/\d\d/,i=/\d\d?/,e=/\d*[^-_:/,()\s\d]+/,u={},s=function(o){return(o=+o)+(o>68?1900:2e3)},a=function(o){return function(c){this[o]=+c}},d=[/[+-]\d\d:?(\d\d)?|Z/,function(o){(this.zone||(this.zone={})).offset=function(c){if(!c||c==="Z")return 0;var D=c.match(/([+-]|\d\d)/g),p=60*D[1]+(+D[2]||0);return p===0?0:D[0]==="+"?-p:p}(o)}],_=function(o){var c=u[o];return c&&(c.indexOf?c:c.s.concat(c.f))},f=function(o,c){var D,p=u.meridiem;if(p){for(var k=1;k<=24;k+=1)if(o.indexOf(p(k,0,c))>-1){D=k>12;break}}else D=o===(c?"pm":"PM");return D},y={A:[e,function(o){this.afternoon=f(o,!1)}],a:[e,function(o){this.afternoon=f(o,!0)}],S:[/\d/,function(o){this.milliseconds=100*+o}],SS:[r,function(o){this.milliseconds=10*+o}],SSS:[/\d{3}/,function(o){this.milliseconds=+o}],s:[i,a("seconds")],ss:[i,a("seconds")],m:[i,a("minutes")],mm:[i,a("minutes")],H:[i,a("hours")],h:[i,a("hours")],HH:[i,a("hours")],hh:[i,a("hours")],D:[i,a("day")],DD:[r,a("day")],Do:[e,function(o){var c=u.ordinal,D=o.match(/\d+/);if(this.day=D[0],c)for(var p=1;p<=31;p+=1)c(p).replace(/\[|\]/g,"")===o&&(this.day=p)}],M:[i,a("month")],MM:[r,a("month")],MMM:[e,function(o){var c=_("months"),D=(_("monthsShort")||c.map(function(p){return p.slice(0,3)})).indexOf(o)+1;if(D<1)throw new Error;this.month=D%12||D}],MMMM:[e,function(o){var c=_("months").indexOf(o)+1;if(c<1)throw new Error;this.month=c%12||c}],Y:[/[+-]?\d+/,a("year")],YY:[r,function(o){this.year=s(o)}],YYYY:[/\d{4}/,a("year")],Z:d,ZZ:d};function l(o){var c,D;c=o,D=u&&u.formats;for(var p=(o=c.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(j,b,N){var U=N&&N.toUpperCase();return b||D[N]||n[N]||D[U].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(v,h,m){return h||m.slice(1)})})).match(t),k=p.length,T=0;T<k;T+=1){var S=p[T],C=y[S],O=C&&C[0],x=C&&C[1];p[T]=x?{regex:O,parser:x}:S.replace(/^\[|\]$/g,"")}return function(j){for(var b={},N=0,U=0;N<k;N+=1){var v=p[N];if(typeof v=="string")U+=v.length;else{var h=v.regex,m=v.parser,Y=j.slice(U),M=h.exec(Y)[0];m.call(b,M),j=j.replace(M,"")}}return function(g){var L=g.afternoon;if(L!==void 0){var $=g.hours;L?$<12&&(g.hours+=12):$===12&&(g.hours=0),delete g.afternoon}}(b),b}}return function(o,c,D){D.p.customParseFormat=!0,o&&o.parseTwoDigitYear&&(s=o.parseTwoDigitYear);var p=c.prototype,k=p.parse;p.parse=function(T){var S=T.date,C=T.utc,O=T.args;this.$u=C;var x=O[1];if(typeof x=="string"){var j=O[2]===!0,b=O[3]===!0,N=j||b,U=O[2];b&&(U=O[2]),u=this.$locale(),!j&&U&&(u=D.Ls[U]),this.$d=function(Y,M,g){try{if(["x","X"].indexOf(M)>-1)return new Date((M==="X"?1e3:1)*Y);var L=l(M)(Y),$=L.year,I=L.month,q=L.day,E=L.hours,W=L.minutes,X=L.seconds,B=L.milliseconds,Q=L.zone,Z=new Date,F=q||($||I?1:Z.getDate()),R=$||Z.getFullYear(),V=0;$&&!I||(V=I>0?I-1:Z.getMonth());var ee=E||0,Me=W||0,ye=X||0,Ye=B||0;return Q?new Date(Date.UTC(R,V,F,ee,Me,ye,Ye+60*Q.offset*1e3)):g?new Date(Date.UTC(R,V,F,ee,Me,ye,Ye)):new Date(R,V,F,ee,Me,ye,Ye)}catch{return new Date("")}}(S,x,C),this.init(),U&&U!==!0&&(this.$L=this.locale(U).$L),N&&S!=this.format(x)&&(this.$d=new Date("")),u={}}else if(x instanceof Array)for(var v=x.length,h=1;h<=v;h+=1){O[1]=x[h-1];var m=D.apply(this,O);if(m.isValid()){this.$d=m.$d,this.$L=m.$L,this.init();break}h===v&&(this.$d=new Date(""))}else k.call(this,T)}}})});var on=H((be,ke)=>{(function(n,t){typeof be=="object"&&typeof ke<"u"?ke.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_plugin_localeData=t()})(be,function(){"use strict";return function(n,t,r){var i=t.prototype,e=function(_){return _&&(_.indexOf?_:_.s)},u=function(_,f,y,l,o){var c=_.name?_:_.$locale(),D=e(c[f]),p=e(c[y]),k=D||p.map(function(S){return S.slice(0,l)});if(!o)return k;var T=c.weekStart;return k.map(function(S,C){return k[(C+(T||0))%7]})},s=function(){return r.Ls[r.locale()]},a=function(_,f){return _.formats[f]||function(y){return y.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(l,o,c){return o||c.slice(1)})}(_.formats[f.toUpperCase()])},d=function(){var _=this;return{months:function(f){return f?f.format("MMMM"):u(_,"months")},monthsShort:function(f){return f?f.format("MMM"):u(_,"monthsShort","months",3)},firstDayOfWeek:function(){return _.$locale().weekStart||0},weekdays:function(f){return f?f.format("dddd"):u(_,"weekdays")},weekdaysMin:function(f){return f?f.format("dd"):u(_,"weekdaysMin","weekdays",2)},weekdaysShort:function(f){return f?f.format("ddd"):u(_,"weekdaysShort","weekdays",3)},longDateFormat:function(f){return a(_.$locale(),f)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};i.localeData=function(){return d.bind(this)()},r.localeData=function(){var _=s();return{firstDayOfWeek:function(){return _.weekStart||0},weekdays:function(){return r.weekdays()},weekdaysShort:function(){return r.weekdaysShort()},weekdaysMin:function(){return r.weekdaysMin()},months:function(){return r.months()},monthsShort:function(){return r.monthsShort()},longDateFormat:function(f){return a(_,f)},meridiem:_.meridiem,ordinal:_.ordinal}},r.months=function(){return u(s(),"months")},r.monthsShort=function(){return u(s(),"monthsShort","months",3)},r.weekdays=function(_){return u(s(),"weekdays",null,null,_)},r.weekdaysShort=function(_){return u(s(),"weekdaysShort","weekdays",3,_)},r.weekdaysMin=function(_){return u(s(),"weekdaysMin","weekdays",2,_)}}})});var dn=H((He,Te)=>{(function(n,t){typeof He=="object"&&typeof Te<"u"?Te.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_plugin_timezone=t()})(He,function(){"use strict";var n={year:0,month:1,day:2,hour:3,minute:4,second:5},t={};return function(r,i,e){var u,s=function(f,y,l){l===void 0&&(l={});var o=new Date(f),c=function(D,p){p===void 0&&(p={});var k=p.timeZoneName||"short",T=D+"|"+k,S=t[T];return S||(S=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:D,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:k}),t[T]=S),S}(y,l);return c.formatToParts(o)},a=function(f,y){for(var l=s(f,y),o=[],c=0;c<l.length;c+=1){var D=l[c],p=D.type,k=D.value,T=n[p];T>=0&&(o[T]=parseInt(k,10))}var S=o[3],C=S===24?0:S,O=o[0]+"-"+o[1]+"-"+o[2]+" "+C+":"+o[4]+":"+o[5]+":000",x=+f;return(e.utc(O).valueOf()-(x-=x%1e3))/6e4},d=i.prototype;d.tz=function(f,y){f===void 0&&(f=u);var l=this.utcOffset(),o=this.toDate(),c=o.toLocaleString("en-US",{timeZone:f}),D=Math.round((o-new Date(c))/1e3/60),p=e(c).$set("millisecond",this.$ms).utcOffset(15*-Math.round(o.getTimezoneOffset()/15)-D,!0);if(y){var k=p.utcOffset();p=p.add(l-k,"minute")}return p.$x.$timezone=f,p},d.offsetName=function(f){var y=this.$x.$timezone||e.tz.guess(),l=s(this.valueOf(),y,{timeZoneName:f}).find(function(o){return o.type.toLowerCase()==="timezonename"});return l&&l.value};var _=d.startOf;d.startOf=function(f,y){if(!this.$x||!this.$x.$timezone)return _.call(this,f,y);var l=e(this.format("YYYY-MM-DD HH:mm:ss:SSS"));return _.call(l,f,y).tz(this.$x.$timezone,!0)},e.tz=function(f,y,l){var o=l&&y,c=l||y||u,D=a(+e(),c);if(typeof f!="string")return e(f).tz(c);var p=function(C,O,x){var j=C-60*O*1e3,b=a(j,x);if(O===b)return[j,O];var N=a(j-=60*(b-O)*1e3,x);return b===N?[j,b]:[C-60*Math.min(b,N)*1e3,Math.max(b,N)]}(e.utc(f,o).valueOf(),D,c),k=p[0],T=p[1],S=e(k).utcOffset(T);return S.$x.$timezone=c,S},e.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},e.tz.setDefault=function(f){u=f}}})});var _n=H((je,we)=>{(function(n,t){typeof je=="object"&&typeof we<"u"?we.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_plugin_utc=t()})(je,function(){"use strict";var n="minute",t=/[+-]\d\d(?::?\d\d)?/g,r=/([+-]|\d\d)/g;return function(i,e,u){var s=e.prototype;u.utc=function(o){var c={date:o,utc:!0,args:arguments};return new e(c)},s.utc=function(o){var c=u(this.toDate(),{locale:this.$L,utc:!0});return o?c.add(this.utcOffset(),n):c},s.local=function(){return u(this.toDate(),{locale:this.$L,utc:!1})};var a=s.parse;s.parse=function(o){o.utc&&(this.$u=!0),this.$utils().u(o.$offset)||(this.$offset=o.$offset),a.call(this,o)};var d=s.init;s.init=function(){if(this.$u){var o=this.$d;this.$y=o.getUTCFullYear(),this.$M=o.getUTCMonth(),this.$D=o.getUTCDate(),this.$W=o.getUTCDay(),this.$H=o.getUTCHours(),this.$m=o.getUTCMinutes(),this.$s=o.getUTCSeconds(),this.$ms=o.getUTCMilliseconds()}else d.call(this)};var _=s.utcOffset;s.utcOffset=function(o,c){var D=this.$utils().u;if(D(o))return this.$u?0:D(this.$offset)?_.call(this):this.$offset;if(typeof o=="string"&&(o=function(S){S===void 0&&(S="");var C=S.match(t);if(!C)return null;var O=(""+C[0]).match(r)||["-",0,0],x=O[0],j=60*+O[1]+ +O[2];return j===0?0:x==="+"?j:-j}(o),o===null))return this;var p=Math.abs(o)<=16?60*o:o,k=this;if(c)return k.$offset=p,k.$u=o===0,k;if(o!==0){var T=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(k=this.local().add(p+T,n)).$offset=p,k.$x.$localOffset=T}else k=this.utc();return k};var f=s.format;s.format=function(o){var c=o||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return f.call(this,c)},s.valueOf=function(){var o=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*o},s.isUTC=function(){return!!this.$u},s.toISOString=function(){return this.toDate().toISOString()},s.toString=function(){return this.toDate().toUTCString()};var y=s.toDate;s.toDate=function(o){return o==="s"&&this.$offset?u(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():y.call(this)};var l=s.diff;s.diff=function(o,c,D){if(o&&this.$u===o.$u)return l.call(this,o,c,D);var p=this.local(),k=u(o).local();return l.call(p,k,c,D)}}})});var w=H(($e,Ce)=>{(function(n,t){typeof $e=="object"&&typeof Ce<"u"?Ce.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs=t()})($e,function(){"use strict";var n=1e3,t=6e4,r=36e5,i="millisecond",e="second",u="minute",s="hour",a="day",d="week",_="month",f="quarter",y="year",l="date",o="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,D=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(v){var h=["th","st","nd","rd"],m=v%100;return"["+v+(h[(m-20)%10]||h[m]||h[0])+"]"}},k=function(v,h,m){var Y=String(v);return!Y||Y.length>=h?v:""+Array(h+1-Y.length).join(m)+v},T={s:k,z:function(v){var h=-v.utcOffset(),m=Math.abs(h),Y=Math.floor(m/60),M=m%60;return(h<=0?"+":"-")+k(Y,2,"0")+":"+k(M,2,"0")},m:function v(h,m){if(h.date()<m.date())return-v(m,h);var Y=12*(m.year()-h.year())+(m.month()-h.month()),M=h.clone().add(Y,_),g=m-M<0,L=h.clone().add(Y+(g?-1:1),_);return+(-(Y+(m-M)/(g?M-L:L-M))||0)},a:function(v){return v<0?Math.ceil(v)||0:Math.floor(v)},p:function(v){return{M:_,y,w:d,d:a,D:l,h:s,m:u,s:e,ms:i,Q:f}[v]||String(v||"").toLowerCase().replace(/s$/,"")},u:function(v){return v===void 0}},S="en",C={};C[S]=p;var O=function(v){return v instanceof N},x=function v(h,m,Y){var M;if(!h)return S;if(typeof h=="string"){var g=h.toLowerCase();C[g]&&(M=g),m&&(C[g]=m,M=g);var L=h.split("-");if(!M&&L.length>1)return v(L[0])}else{var $=h.name;C[$]=h,M=$}return!Y&&M&&(S=M),M||!Y&&S},j=function(v,h){if(O(v))return v.clone();var m=typeof h=="object"?h:{};return m.date=v,m.args=arguments,new N(m)},b=T;b.l=x,b.i=O,b.w=function(v,h){return j(v,{locale:h.$L,utc:h.$u,x:h.$x,$offset:h.$offset})};var N=function(){function v(m){this.$L=x(m.locale,null,!0),this.parse(m)}var h=v.prototype;return h.parse=function(m){this.$d=function(Y){var M=Y.date,g=Y.utc;if(M===null)return new Date(NaN);if(b.u(M))return new Date;if(M instanceof Date)return new Date(M);if(typeof M=="string"&&!/Z$/i.test(M)){var L=M.match(c);if(L){var $=L[2]-1||0,I=(L[7]||"0").substring(0,3);return g?new Date(Date.UTC(L[1],$,L[3]||1,L[4]||0,L[5]||0,L[6]||0,I)):new Date(L[1],$,L[3]||1,L[4]||0,L[5]||0,L[6]||0,I)}}return new Date(M)}(m),this.$x=m.x||{},this.init()},h.init=function(){var m=this.$d;this.$y=m.getFullYear(),this.$M=m.getMonth(),this.$D=m.getDate(),this.$W=m.getDay(),this.$H=m.getHours(),this.$m=m.getMinutes(),this.$s=m.getSeconds(),this.$ms=m.getMilliseconds()},h.$utils=function(){return b},h.isValid=function(){return this.$d.toString()!==o},h.isSame=function(m,Y){var M=j(m);return this.startOf(Y)<=M&&M<=this.endOf(Y)},h.isAfter=function(m,Y){return j(m)<this.startOf(Y)},h.isBefore=function(m,Y){return this.endOf(Y)<j(m)},h.$g=function(m,Y,M){return b.u(m)?this[Y]:this.set(M,m)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(m,Y){var M=this,g=!!b.u(Y)||Y,L=b.p(m),$=function(Z,F){var R=b.w(M.$u?Date.UTC(M.$y,F,Z):new Date(M.$y,F,Z),M);return g?R:R.endOf(a)},I=function(Z,F){return b.w(M.toDate()[Z].apply(M.toDate("s"),(g?[0,0,0,0]:[23,59,59,999]).slice(F)),M)},q=this.$W,E=this.$M,W=this.$D,X="set"+(this.$u?"UTC":"");switch(L){case y:return g?$(1,0):$(31,11);case _:return g?$(1,E):$(0,E+1);case d:var B=this.$locale().weekStart||0,Q=(q<B?q+7:q)-B;return $(g?W-Q:W+(6-Q),E);case a:case l:return I(X+"Hours",0);case s:return I(X+"Minutes",1);case u:return I(X+"Seconds",2);case e:return I(X+"Milliseconds",3);default:return this.clone()}},h.endOf=function(m){return this.startOf(m,!1)},h.$set=function(m,Y){var M,g=b.p(m),L="set"+(this.$u?"UTC":""),$=(M={},M[a]=L+"Date",M[l]=L+"Date",M[_]=L+"Month",M[y]=L+"FullYear",M[s]=L+"Hours",M[u]=L+"Minutes",M[e]=L+"Seconds",M[i]=L+"Milliseconds",M)[g],I=g===a?this.$D+(Y-this.$W):Y;if(g===_||g===y){var q=this.clone().set(l,1);q.$d[$](I),q.init(),this.$d=q.set(l,Math.min(this.$D,q.daysInMonth())).$d}else $&&this.$d[$](I);return this.init(),this},h.set=function(m,Y){return this.clone().$set(m,Y)},h.get=function(m){return this[b.p(m)]()},h.add=function(m,Y){var M,g=this;m=Number(m);var L=b.p(Y),$=function(E){var W=j(g);return b.w(W.date(W.date()+Math.round(E*m)),g)};if(L===_)return this.set(_,this.$M+m);if(L===y)return this.set(y,this.$y+m);if(L===a)return $(1);if(L===d)return $(7);var I=(M={},M[u]=t,M[s]=r,M[e]=n,M)[L]||1,q=this.$d.getTime()+m*I;return b.w(q,this)},h.subtract=function(m,Y){return this.add(-1*m,Y)},h.format=function(m){var Y=this,M=this.$locale();if(!this.isValid())return M.invalidDate||o;var g=m||"YYYY-MM-DDTHH:mm:ssZ",L=b.z(this),$=this.$H,I=this.$m,q=this.$M,E=M.weekdays,W=M.months,X=M.meridiem,B=function(F,R,V,ee){return F&&(F[R]||F(Y,g))||V[R].slice(0,ee)},Q=function(F){return b.s($%12||12,F,"0")},Z=X||function(F,R,V){var ee=F<12?"AM":"PM";return V?ee.toLowerCase():ee};return g.replace(D,function(F,R){return R||function(V){switch(V){case"YY":return String(Y.$y).slice(-2);case"YYYY":return b.s(Y.$y,4,"0");case"M":return q+1;case"MM":return b.s(q+1,2,"0");case"MMM":return B(M.monthsShort,q,W,3);case"MMMM":return B(W,q);case"D":return Y.$D;case"DD":return b.s(Y.$D,2,"0");case"d":return String(Y.$W);case"dd":return B(M.weekdaysMin,Y.$W,E,2);case"ddd":return B(M.weekdaysShort,Y.$W,E,3);case"dddd":return E[Y.$W];case"H":return String($);case"HH":return b.s($,2,"0");case"h":return Q(1);case"hh":return Q(2);case"a":return Z($,I,!0);case"A":return Z($,I,!1);case"m":return String(I);case"mm":return b.s(I,2,"0");case"s":return String(Y.$s);case"ss":return b.s(Y.$s,2,"0");case"SSS":return b.s(Y.$ms,3,"0");case"Z":return L}return null}(F)||L.replace(":","")})},h.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},h.diff=function(m,Y,M){var g,L=this,$=b.p(Y),I=j(m),q=(I.utcOffset()-this.utcOffset())*t,E=this-I,W=function(){return b.m(L,I)};switch($){case y:g=W()/12;break;case _:g=W();break;case f:g=W()/3;break;case d:g=(E-q)/6048e5;break;case a:g=(E-q)/864e5;break;case s:g=E/r;break;case u:g=E/t;break;case e:g=E/n;break;default:g=E}return M?g:b.a(g)},h.daysInMonth=function(){return this.endOf(_).$D},h.$locale=function(){return C[this.$L]},h.locale=function(m,Y){if(!m)return this.$L;var M=this.clone(),g=x(m,Y,!0);return g&&(M.$L=g),M},h.clone=function(){return b.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},v}(),U=N.prototype;return j.prototype=U,[["$ms",i],["$s",e],["$m",u],["$H",s],["$W",a],["$M",_],["$y",y],["$D",l]].forEach(function(v){U[v[1]]=function(h){return this.$g(h,v[0],v[1])}}),j.extend=function(v,h){return v.$i||(v(h,N,j),v.$i=!0),j},j.locale=x,j.isDayjs=O,j.unix=function(v){return j(1e3*v)},j.en=C[S],j.Ls=C,j.p={},j})});var fn=H((Oe,ze)=>{(function(n,t){typeof Oe=="object"&&typeof ze<"u"?ze.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ar=t(n.dayjs)})(Oe,function(n){"use strict";function t(a){return a&&typeof a=="object"&&"default"in a?a:{default:a}}var r=t(n),i="\u064A\u0646\u0627\u064A\u0631_\u0641\u0628\u0631\u0627\u064A\u0631_\u0645\u0627\u0631\u0633_\u0623\u0628\u0631\u064A\u0644_\u0645\u0627\u064A\u0648_\u064A\u0648\u0646\u064A\u0648_\u064A\u0648\u0644\u064A\u0648_\u0623\u063A\u0633\u0637\u0633_\u0633\u0628\u062A\u0645\u0628\u0631_\u0623\u0643\u062A\u0648\u0628\u0631_\u0646\u0648\u0641\u0645\u0628\u0631_\u062F\u064A\u0633\u0645\u0628\u0631".split("_"),e={1:"\u0661",2:"\u0662",3:"\u0663",4:"\u0664",5:"\u0665",6:"\u0666",7:"\u0667",8:"\u0668",9:"\u0669",0:"\u0660"},u={"\u0661":"1","\u0662":"2","\u0663":"3","\u0664":"4","\u0665":"5","\u0666":"6","\u0667":"7","\u0668":"8","\u0669":"9","\u0660":"0"},s={name:"ar",weekdays:"\u0627\u0644\u0623\u062D\u062F_\u0627\u0644\u0625\u062B\u0646\u064A\u0646_\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621_\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621_\u0627\u0644\u062E\u0645\u064A\u0633_\u0627\u0644\u062C\u0645\u0639\u0629_\u0627\u0644\u0633\u0628\u062A".split("_"),weekdaysShort:"\u0623\u062D\u062F_\u0625\u062B\u0646\u064A\u0646_\u062B\u0644\u0627\u062B\u0627\u0621_\u0623\u0631\u0628\u0639\u0627\u0621_\u062E\u0645\u064A\u0633_\u062C\u0645\u0639\u0629_\u0633\u0628\u062A".split("_"),weekdaysMin:"\u062D_\u0646_\u062B_\u0631_\u062E_\u062C_\u0633".split("_"),months:i,monthsShort:i,weekStart:6,relativeTime:{future:"\u0628\u0639\u062F %s",past:"\u0645\u0646\u0630 %s",s:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",m:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",mm:"%d \u062F\u0642\u0627\u0626\u0642",h:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",hh:"%d \u0633\u0627\u0639\u0627\u062A",d:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",dd:"%d \u0623\u064A\u0627\u0645",M:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",MM:"%d \u0623\u0634\u0647\u0631",y:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F",yy:"%d \u0623\u0639\u0648\u0627\u0645"},preparse:function(a){return a.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(d){return u[d]}).replace(/،/g,",")},postformat:function(a){return a.replace(/\d/g,function(d){return e[d]}).replace(/,/g,"\u060C")},ordinal:function(a){return a},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/\u200FM/\u200FYYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"}};return r.default.locale(s,null,!0),s})});var ln=H((Ae,Ie)=>{(function(n,t){typeof Ae=="object"&&typeof Ie<"u"?Ie.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_bs=t(n.dayjs)})(Ae,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"bs",weekdays:"nedjelja_ponedjeljak_utorak_srijeda_\u010Detvrtak_petak_subota".split("_"),months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),weekStart:1,weekdaysShort:"ned._pon._uto._sri._\u010Det._pet._sub.".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),weekdaysMin:"ne_po_ut_sr_\u010De_pe_su".split("_"),ordinal:function(e){return e},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"}};return r.default.locale(i,null,!0),i})});var mn=H((xe,qe)=>{(function(n,t){typeof xe=="object"&&typeof qe<"u"?qe.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ca=t(n.dayjs)})(xe,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"ca",weekdays:"Diumenge_Dilluns_Dimarts_Dimecres_Dijous_Divendres_Dissabte".split("_"),weekdaysShort:"Dg._Dl._Dt._Dc._Dj._Dv._Ds.".split("_"),weekdaysMin:"Dg_Dl_Dt_Dc_Dj_Dv_Ds".split("_"),months:"Gener_Febrer_Mar\xE7_Abril_Maig_Juny_Juliol_Agost_Setembre_Octubre_Novembre_Desembre".split("_"),monthsShort:"Gen._Febr._Mar\xE7_Abr._Maig_Juny_Jul._Ag._Set._Oct._Nov._Des.".split("_"),weekStart:1,formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",ll:"D MMM YYYY",lll:"D MMM YYYY, H:mm",llll:"ddd D MMM YYYY, H:mm"},relativeTime:{future:"d'aqu\xED %s",past:"fa %s",s:"uns segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},ordinal:function(e){return""+e+(e===1||e===3?"r":e===2?"n":e===4?"t":"\xE8")}};return r.default.locale(i,null,!0),i})});var cn=H((Ne,Ee)=>{(function(n,t){typeof Ne=="object"&&typeof Ee<"u"?Ee.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_cs=t(n.dayjs)})(Ne,function(n){"use strict";function t(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var r=t(n);function i(s){return s>1&&s<5&&~~(s/10)!=1}function e(s,a,d,_){var f=s+" ";switch(d){case"s":return a||_?"p\xE1r sekund":"p\xE1r sekundami";case"m":return a?"minuta":_?"minutu":"minutou";case"mm":return a||_?f+(i(s)?"minuty":"minut"):f+"minutami";case"h":return a?"hodina":_?"hodinu":"hodinou";case"hh":return a||_?f+(i(s)?"hodiny":"hodin"):f+"hodinami";case"d":return a||_?"den":"dnem";case"dd":return a||_?f+(i(s)?"dny":"dn\xED"):f+"dny";case"M":return a||_?"m\u011Bs\xEDc":"m\u011Bs\xEDcem";case"MM":return a||_?f+(i(s)?"m\u011Bs\xEDce":"m\u011Bs\xEDc\u016F"):f+"m\u011Bs\xEDci";case"y":return a||_?"rok":"rokem";case"yy":return a||_?f+(i(s)?"roky":"let"):f+"lety"}}var u={name:"cs",weekdays:"ned\u011Ble_pond\u011Bl\xED_\xFAter\xFD_st\u0159eda_\u010Dtvrtek_p\xE1tek_sobota".split("_"),weekdaysShort:"ne_po_\xFAt_st_\u010Dt_p\xE1_so".split("_"),weekdaysMin:"ne_po_\xFAt_st_\u010Dt_p\xE1_so".split("_"),months:"leden_\xFAnor_b\u0159ezen_duben_kv\u011Bten_\u010Derven_\u010Dervenec_srpen_z\xE1\u0159\xED_\u0159\xEDjen_listopad_prosinec".split("_"),monthsShort:"led_\xFAno_b\u0159e_dub_kv\u011B_\u010Dvn_\u010Dvc_srp_z\xE1\u0159_\u0159\xEDj_lis_pro".split("_"),weekStart:1,yearStart:4,ordinal:function(s){return s+"."},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},relativeTime:{future:"za %s",past:"p\u0159ed %s",s:e,m:e,mm:e,h:e,hh:e,d:e,dd:e,M:e,MM:e,y:e,yy:e}};return r.default.locale(u,null,!0),u})});var hn=H((Fe,Je)=>{(function(n,t){typeof Fe=="object"&&typeof Je<"u"?Je.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_cy=t(n.dayjs)})(Fe,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"cy",weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),weekStart:1,weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},relativeTime:{future:"mewn %s",past:"%s yn \xF4l",s:"ychydig eiliadau",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"}};return r.default.locale(i,null,!0),i})});var Mn=H((Ue,We)=>{(function(n,t){typeof Ue=="object"&&typeof We<"u"?We.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_da=t(n.dayjs)})(Ue,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"da",weekdays:"s\xF8ndag_mandag_tirsdag_onsdag_torsdag_fredag_l\xF8rdag".split("_"),weekdaysShort:"s\xF8n._man._tirs._ons._tors._fre._l\xF8r.".split("_"),weekdaysMin:"s\xF8._ma._ti._on._to._fr._l\xF8.".split("_"),months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj_juni_juli_aug._sept._okt._nov._dec.".split("_"),weekStart:1,ordinal:function(e){return e+"."},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},relativeTime:{future:"om %s",past:"%s siden",s:"f\xE5 sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en m\xE5ned",MM:"%d m\xE5neder",y:"et \xE5r",yy:"%d \xE5r"}};return r.default.locale(i,null,!0),i})});var yn=H((Pe,Re)=>{(function(n,t){typeof Pe=="object"&&typeof Re<"u"?Re.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_de=t(n.dayjs)})(Pe,function(n){"use strict";function t(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var r=t(n),i={s:"ein paar Sekunden",m:["eine Minute","einer Minute"],mm:"%d Minuten",h:["eine Stunde","einer Stunde"],hh:"%d Stunden",d:["ein Tag","einem Tag"],dd:["%d Tage","%d Tagen"],M:["ein Monat","einem Monat"],MM:["%d Monate","%d Monaten"],y:["ein Jahr","einem Jahr"],yy:["%d Jahre","%d Jahren"]};function e(s,a,d){var _=i[d];return Array.isArray(_)&&(_=_[a?0:1]),_.replace("%d",s)}var u={name:"de",weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),months:"Januar_Februar_M\xE4rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._M\xE4rz_Apr._Mai_Juni_Juli_Aug._Sept._Okt._Nov._Dez.".split("_"),ordinal:function(s){return s+"."},weekStart:1,yearStart:4,formats:{LTS:"HH:mm:ss",LT:"HH:mm",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},relativeTime:{future:"in %s",past:"vor %s",s:e,m:e,mm:e,h:e,hh:e,d:e,dd:e,M:e,MM:e,y:e,yy:e}};return r.default.locale(u,null,!0),u})});var Yn=H((Ze,Ve)=>{(function(n,t){typeof Ze=="object"&&typeof Ve<"u"?Ve.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_en=t()})(Ze,function(){"use strict";return{name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(n){var t=["th","st","nd","rd"],r=n%100;return"["+n+(t[(r-20)%10]||t[r]||t[0])+"]"}}})});var pn=H((Ge,Ke)=>{(function(n,t){typeof Ge=="object"&&typeof Ke<"u"?Ke.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_es=t(n.dayjs)})(Ge,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"es",monthsShort:"ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),weekdays:"domingo_lunes_martes_mi\xE9rcoles_jueves_viernes_s\xE1bado".split("_"),weekdaysShort:"dom._lun._mar._mi\xE9._jue._vie._s\xE1b.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_s\xE1".split("_"),months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),weekStart:1,formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un d\xEDa",dd:"%d d\xEDas",M:"un mes",MM:"%d meses",y:"un a\xF1o",yy:"%d a\xF1os"},ordinal:function(e){return e+"\xBA"}};return r.default.locale(i,null,!0),i})});var Dn=H((Be,Xe)=>{(function(n,t){typeof Be=="object"&&typeof Xe<"u"?Xe.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_fa=t(n.dayjs)})(Be,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"fa",weekdays:"\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647_\u062F\u0648\u0634\u0646\u0628\u0647_\u0633\u0647\u200C\u0634\u0646\u0628\u0647_\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647_\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647_\u062C\u0645\u0639\u0647_\u0634\u0646\u0628\u0647".split("_"),weekdaysShort:"\u06CC\u06A9\u200C_\u062F\u0648_\u0633\u0647\u200C_\u0686\u0647_\u067E\u0646_\u062C\u0645_\u0634\u0646".split("_"),weekdaysMin:"\u06CC_\u062F_\u0633_\u0686_\u067E_\u062C_\u0634".split("_"),weekStart:6,months:"\u0641\u0631\u0648\u0631\u062F\u06CC\u0646_\u0627\u0631\u062F\u06CC\u0628\u0647\u0634\u062A_\u062E\u0631\u062F\u0627\u062F_\u062A\u06CC\u0631_\u0645\u0631\u062F\u0627\u062F_\u0634\u0647\u0631\u06CC\u0648\u0631_\u0645\u0647\u0631_\u0622\u0628\u0627\u0646_\u0622\u0630\u0631_\u062F\u06CC_\u0628\u0647\u0645\u0646_\u0627\u0633\u0641\u0646\u062F".split("_"),monthsShort:"\u0641\u0631\u0648_\u0627\u0631\u062F_\u062E\u0631\u062F_\u062A\u06CC\u0631_\u0645\u0631\u062F_\u0634\u0647\u0631_\u0645\u0647\u0631_\u0622\u0628\u0627_\u0622\u0630\u0631_\u062F\u06CC_\u0628\u0647\u0645_\u0627\u0633\u0641".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},relativeTime:{future:"\u062F\u0631 %s",past:"%s \u0642\u0628\u0644",s:"\u0686\u0646\u062F \u062B\u0627\u0646\u06CC\u0647",m:"\u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",mm:"%d \u062F\u0642\u06CC\u0642\u0647",h:"\u06CC\u06A9 \u0633\u0627\u0639\u062A",hh:"%d \u0633\u0627\u0639\u062A",d:"\u06CC\u06A9 \u0631\u0648\u0632",dd:"%d \u0631\u0648\u0632",M:"\u06CC\u06A9 \u0645\u0627\u0647",MM:"%d \u0645\u0627\u0647",y:"\u06CC\u06A9 \u0633\u0627\u0644",yy:"%d \u0633\u0627\u0644"}};return r.default.locale(i,null,!0),i})});var Ln=H((Qe,et)=>{(function(n,t){typeof Qe=="object"&&typeof et<"u"?et.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_fi=t(n.dayjs)})(Qe,function(n){"use strict";function t(u){return u&&typeof u=="object"&&"default"in u?u:{default:u}}var r=t(n);function i(u,s,a,d){var _={s:"muutama sekunti",m:"minuutti",mm:"%d minuuttia",h:"tunti",hh:"%d tuntia",d:"p\xE4iv\xE4",dd:"%d p\xE4iv\xE4\xE4",M:"kuukausi",MM:"%d kuukautta",y:"vuosi",yy:"%d vuotta",numbers:"nolla_yksi_kaksi_kolme_nelj\xE4_viisi_kuusi_seitsem\xE4n_kahdeksan_yhdeks\xE4n".split("_")},f={s:"muutaman sekunnin",m:"minuutin",mm:"%d minuutin",h:"tunnin",hh:"%d tunnin",d:"p\xE4iv\xE4n",dd:"%d p\xE4iv\xE4n",M:"kuukauden",MM:"%d kuukauden",y:"vuoden",yy:"%d vuoden",numbers:"nollan_yhden_kahden_kolmen_nelj\xE4n_viiden_kuuden_seitsem\xE4n_kahdeksan_yhdeks\xE4n".split("_")},y=d&&!s?f:_,l=y[a];return u<10?l.replace("%d",y.numbers[u]):l.replace("%d",u)}var e={name:"fi",weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kes\xE4kuu_hein\xE4kuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kes\xE4_hein\xE4_elo_syys_loka_marras_joulu".split("_"),ordinal:function(u){return u+"."},weekStart:1,yearStart:4,relativeTime:{future:"%s p\xE4\xE4st\xE4",past:"%s sitten",s:i,m:i,mm:i,h:i,hh:i,d:i,dd:i,M:i,MM:i,y:i,yy:i},formats:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM[ta] YYYY",LLL:"D. MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, D. MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"D. MMM YYYY",lll:"D. MMM YYYY, [klo] HH.mm",llll:"ddd, D. MMM YYYY, [klo] HH.mm"}};return r.default.locale(e,null,!0),e})});var vn=H((tt,nt)=>{(function(n,t){typeof tt=="object"&&typeof nt<"u"?nt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_fr=t(n.dayjs)})(tt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_f\xE9vrier_mars_avril_mai_juin_juillet_ao\xFBt_septembre_octobre_novembre_d\xE9cembre".split("_"),monthsShort:"janv._f\xE9vr._mars_avr._mai_juin_juil._ao\xFBt_sept._oct._nov._d\xE9c.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(e){return""+e+(e===1?"er":"")}};return r.default.locale(i,null,!0),i})});var gn=H((it,rt)=>{(function(n,t){typeof it=="object"&&typeof rt<"u"?rt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_hi=t(n.dayjs)})(it,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"hi",weekdays:"\u0930\u0935\u093F\u0935\u093E\u0930_\u0938\u094B\u092E\u0935\u093E\u0930_\u092E\u0902\u0917\u0932\u0935\u093E\u0930_\u092C\u0941\u0927\u0935\u093E\u0930_\u0917\u0941\u0930\u0942\u0935\u093E\u0930_\u0936\u0941\u0915\u094D\u0930\u0935\u093E\u0930_\u0936\u0928\u093F\u0935\u093E\u0930".split("_"),months:"\u091C\u0928\u0935\u0930\u0940_\u092B\u093C\u0930\u0935\u0930\u0940_\u092E\u093E\u0930\u094D\u091A_\u0905\u092A\u094D\u0930\u0948\u0932_\u092E\u0908_\u091C\u0942\u0928_\u091C\u0941\u0932\u093E\u0908_\u0905\u0917\u0938\u094D\u0924_\u0938\u093F\u0924\u092E\u094D\u092C\u0930_\u0905\u0915\u094D\u091F\u0942\u092C\u0930_\u0928\u0935\u092E\u094D\u092C\u0930_\u0926\u093F\u0938\u092E\u094D\u092C\u0930".split("_"),weekdaysShort:"\u0930\u0935\u093F_\u0938\u094B\u092E_\u092E\u0902\u0917\u0932_\u092C\u0941\u0927_\u0917\u0941\u0930\u0942_\u0936\u0941\u0915\u094D\u0930_\u0936\u0928\u093F".split("_"),monthsShort:"\u091C\u0928._\u092B\u093C\u0930._\u092E\u093E\u0930\u094D\u091A_\u0905\u092A\u094D\u0930\u0948._\u092E\u0908_\u091C\u0942\u0928_\u091C\u0941\u0932._\u0905\u0917._\u0938\u093F\u0924._\u0905\u0915\u094D\u091F\u0942._\u0928\u0935._\u0926\u093F\u0938.".split("_"),weekdaysMin:"\u0930_\u0938\u094B_\u092E\u0902_\u092C\u0941_\u0917\u0941_\u0936\u0941_\u0936".split("_"),ordinal:function(e){return e},formats:{LT:"A h:mm \u092C\u091C\u0947",LTS:"A h:mm:ss \u092C\u091C\u0947",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm \u092C\u091C\u0947",LLLL:"dddd, D MMMM YYYY, A h:mm \u092C\u091C\u0947"},relativeTime:{future:"%s \u092E\u0947\u0902",past:"%s \u092A\u0939\u0932\u0947",s:"\u0915\u0941\u091B \u0939\u0940 \u0915\u094D\u0937\u0923",m:"\u090F\u0915 \u092E\u093F\u0928\u091F",mm:"%d \u092E\u093F\u0928\u091F",h:"\u090F\u0915 \u0918\u0902\u091F\u093E",hh:"%d \u0918\u0902\u091F\u0947",d:"\u090F\u0915 \u0926\u093F\u0928",dd:"%d \u0926\u093F\u0928",M:"\u090F\u0915 \u092E\u0939\u0940\u0928\u0947",MM:"%d \u092E\u0939\u0940\u0928\u0947",y:"\u090F\u0915 \u0935\u0930\u094D\u0937",yy:"%d \u0935\u0930\u094D\u0937"}};return r.default.locale(i,null,!0),i})});var Sn=H((st,at)=>{(function(n,t){typeof st=="object"&&typeof at<"u"?at.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_hu=t(n.dayjs)})(st,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"hu",weekdays:"vas\xE1rnap_h\xE9tf\u0151_kedd_szerda_cs\xFCt\xF6rt\xF6k_p\xE9ntek_szombat".split("_"),weekdaysShort:"vas_h\xE9t_kedd_sze_cs\xFCt_p\xE9n_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),months:"janu\xE1r_febru\xE1r_m\xE1rcius_\xE1prilis_m\xE1jus_j\xFAnius_j\xFAlius_augusztus_szeptember_okt\xF3ber_november_december".split("_"),monthsShort:"jan_feb_m\xE1rc_\xE1pr_m\xE1j_j\xFAn_j\xFAl_aug_szept_okt_nov_dec".split("_"),ordinal:function(e){return e+"."},weekStart:1,relativeTime:{future:"%s m\xFAlva",past:"%s",s:function(e,u,s,a){return"n\xE9h\xE1ny m\xE1sodperc"+(a||u?"":"e")},m:function(e,u,s,a){return"egy perc"+(a||u?"":"e")},mm:function(e,u,s,a){return e+" perc"+(a||u?"":"e")},h:function(e,u,s,a){return"egy "+(a||u?"\xF3ra":"\xF3r\xE1ja")},hh:function(e,u,s,a){return e+" "+(a||u?"\xF3ra":"\xF3r\xE1ja")},d:function(e,u,s,a){return"egy "+(a||u?"nap":"napja")},dd:function(e,u,s,a){return e+" "+(a||u?"nap":"napja")},M:function(e,u,s,a){return"egy "+(a||u?"h\xF3nap":"h\xF3napja")},MM:function(e,u,s,a){return e+" "+(a||u?"h\xF3nap":"h\xF3napja")},y:function(e,u,s,a){return"egy "+(a||u?"\xE9v":"\xE9ve")},yy:function(e,u,s,a){return e+" "+(a||u?"\xE9v":"\xE9ve")}},formats:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"}};return r.default.locale(i,null,!0),i})});var bn=H((ut,ot)=>{(function(n,t){typeof ut=="object"&&typeof ot<"u"?ot.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_hy_am=t(n.dayjs)})(ut,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"hy-am",weekdays:"\u056F\u056B\u0580\u0561\u056F\u056B_\u0565\u0580\u056F\u0578\u0582\u0577\u0561\u0562\u0569\u056B_\u0565\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B_\u0579\u0578\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B_\u0570\u056B\u0576\u0563\u0577\u0561\u0562\u0569\u056B_\u0578\u0582\u0580\u0562\u0561\u0569_\u0577\u0561\u0562\u0561\u0569".split("_"),months:"\u0570\u0578\u0582\u0576\u057E\u0561\u0580\u056B_\u0583\u0565\u057F\u0580\u057E\u0561\u0580\u056B_\u0574\u0561\u0580\u057F\u056B_\u0561\u057A\u0580\u056B\u056C\u056B_\u0574\u0561\u0575\u056B\u057D\u056B_\u0570\u0578\u0582\u0576\u056B\u057D\u056B_\u0570\u0578\u0582\u056C\u056B\u057D\u056B_\u0585\u0563\u0578\u057D\u057F\u0578\u057D\u056B_\u057D\u0565\u057A\u057F\u0565\u0574\u0562\u0565\u0580\u056B_\u0570\u0578\u056F\u057F\u0565\u0574\u0562\u0565\u0580\u056B_\u0576\u0578\u0575\u0565\u0574\u0562\u0565\u0580\u056B_\u0564\u0565\u056F\u057F\u0565\u0574\u0562\u0565\u0580\u056B".split("_"),weekStart:1,weekdaysShort:"\u056F\u0580\u056F_\u0565\u0580\u056F_\u0565\u0580\u0584_\u0579\u0580\u0584_\u0570\u0576\u0563_\u0578\u0582\u0580\u0562_\u0577\u0562\u0569".split("_"),monthsShort:"\u0570\u0576\u057E_\u0583\u057F\u0580_\u0574\u0580\u057F_\u0561\u057A\u0580_\u0574\u0575\u057D_\u0570\u0576\u057D_\u0570\u056C\u057D_\u0585\u0563\u057D_\u057D\u057A\u057F_\u0570\u056F\u057F_\u0576\u0574\u0562_\u0564\u056F\u057F".split("_"),weekdaysMin:"\u056F\u0580\u056F_\u0565\u0580\u056F_\u0565\u0580\u0584_\u0579\u0580\u0584_\u0570\u0576\u0563_\u0578\u0582\u0580\u0562_\u0577\u0562\u0569".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY \u0569.",LLL:"D MMMM YYYY \u0569., HH:mm",LLLL:"dddd, D MMMM YYYY \u0569., HH:mm"},relativeTime:{future:"%s \u0570\u0565\u057F\u0578",past:"%s \u0561\u057C\u0561\u057B",s:"\u0574\u056B \u0584\u0561\u0576\u056B \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576",m:"\u0580\u0578\u057A\u0565",mm:"%d \u0580\u0578\u057A\u0565",h:"\u056A\u0561\u0574",hh:"%d \u056A\u0561\u0574",d:"\u0585\u0580",dd:"%d \u0585\u0580",M:"\u0561\u0574\u056B\u057D",MM:"%d \u0561\u0574\u056B\u057D",y:"\u057F\u0561\u0580\u056B",yy:"%d \u057F\u0561\u0580\u056B"}};return r.default.locale(i,null,!0),i})});var kn=H((dt,_t)=>{(function(n,t){typeof dt=="object"&&typeof _t<"u"?_t.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_id=t(n.dayjs)})(dt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"id",weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),weekStart:1,formats:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},ordinal:function(e){return e+"."}};return r.default.locale(i,null,!0),i})});var Hn=H((ft,lt)=>{(function(n,t){typeof ft=="object"&&typeof lt<"u"?lt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_it=t(n.dayjs)})(ft,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"it",weekdays:"domenica_luned\xEC_marted\xEC_mercoled\xEC_gioved\xEC_venerd\xEC_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),weekStart:1,monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"tra %s",past:"%s fa",s:"qualche secondo",m:"un minuto",mm:"%d minuti",h:"un' ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},ordinal:function(e){return e+"\xBA"}};return r.default.locale(i,null,!0),i})});var Tn=H((mt,ct)=>{(function(n,t){typeof mt=="object"&&typeof ct<"u"?ct.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ja=t(n.dayjs)})(mt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"ja",weekdays:"\u65E5\u66DC\u65E5_\u6708\u66DC\u65E5_\u706B\u66DC\u65E5_\u6C34\u66DC\u65E5_\u6728\u66DC\u65E5_\u91D1\u66DC\u65E5_\u571F\u66DC\u65E5".split("_"),weekdaysShort:"\u65E5_\u6708_\u706B_\u6C34_\u6728_\u91D1_\u571F".split("_"),weekdaysMin:"\u65E5_\u6708_\u706B_\u6C34_\u6728_\u91D1_\u571F".split("_"),months:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),monthsShort:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),ordinal:function(e){return e+"\u65E5"},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY\u5E74M\u6708D\u65E5",LLL:"YYYY\u5E74M\u6708D\u65E5 HH:mm",LLLL:"YYYY\u5E74M\u6708D\u65E5 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY\u5E74M\u6708D\u65E5",lll:"YYYY\u5E74M\u6708D\u65E5 HH:mm",llll:"YYYY\u5E74M\u6708D\u65E5(ddd) HH:mm"},meridiem:function(e){return e<12?"\u5348\u524D":"\u5348\u5F8C"},relativeTime:{future:"%s\u5F8C",past:"%s\u524D",s:"\u6570\u79D2",m:"1\u5206",mm:"%d\u5206",h:"1\u6642\u9593",hh:"%d\u6642\u9593",d:"1\u65E5",dd:"%d\u65E5",M:"1\u30F6\u6708",MM:"%d\u30F6\u6708",y:"1\u5E74",yy:"%d\u5E74"}};return r.default.locale(i,null,!0),i})});var jn=H((ht,Mt)=>{(function(n,t){typeof ht=="object"&&typeof Mt<"u"?Mt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ka=t(n.dayjs)})(ht,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"ka",weekdays:"\u10D9\u10D5\u10D8\u10E0\u10D0_\u10DD\u10E0\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8_\u10E1\u10D0\u10DB\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8_\u10DD\u10D7\u10EE\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8_\u10EE\u10E3\u10D7\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8_\u10DE\u10D0\u10E0\u10D0\u10E1\u10D9\u10D4\u10D5\u10D8_\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8".split("_"),weekdaysShort:"\u10D9\u10D5\u10D8_\u10DD\u10E0\u10E8_\u10E1\u10D0\u10DB_\u10DD\u10D7\u10EE_\u10EE\u10E3\u10D7_\u10DE\u10D0\u10E0_\u10E8\u10D0\u10D1".split("_"),weekdaysMin:"\u10D9\u10D5_\u10DD\u10E0_\u10E1\u10D0_\u10DD\u10D7_\u10EE\u10E3_\u10DE\u10D0_\u10E8\u10D0".split("_"),months:"\u10D8\u10D0\u10DC\u10D5\u10D0\u10E0\u10D8_\u10D7\u10D4\u10D1\u10D4\u10E0\u10D5\u10D0\u10DA\u10D8_\u10DB\u10D0\u10E0\u10E2\u10D8_\u10D0\u10DE\u10E0\u10D8\u10DA\u10D8_\u10DB\u10D0\u10D8\u10E1\u10D8_\u10D8\u10D5\u10DC\u10D8\u10E1\u10D8_\u10D8\u10D5\u10DA\u10D8\u10E1\u10D8_\u10D0\u10D2\u10D5\u10D8\u10E1\u10E2\u10DD_\u10E1\u10D4\u10E5\u10E2\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8_\u10DD\u10E5\u10E2\u10DD\u10DB\u10D1\u10D4\u10E0\u10D8_\u10DC\u10DD\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8_\u10D3\u10D4\u10D9\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8".split("_"),monthsShort:"\u10D8\u10D0\u10DC_\u10D7\u10D4\u10D1_\u10DB\u10D0\u10E0_\u10D0\u10DE\u10E0_\u10DB\u10D0\u10D8_\u10D8\u10D5\u10DC_\u10D8\u10D5\u10DA_\u10D0\u10D2\u10D5_\u10E1\u10D4\u10E5_\u10DD\u10E5\u10E2_\u10DC\u10DD\u10D4_\u10D3\u10D4\u10D9".split("_"),weekStart:1,formats:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},relativeTime:{future:"%s \u10E8\u10D4\u10DB\u10D3\u10D4\u10D2",past:"%s \u10EC\u10D8\u10DC",s:"\u10EC\u10D0\u10DB\u10D8",m:"\u10EC\u10E3\u10D7\u10D8",mm:"%d \u10EC\u10E3\u10D7\u10D8",h:"\u10E1\u10D0\u10D0\u10D7\u10D8",hh:"%d \u10E1\u10D0\u10D0\u10D7\u10D8\u10E1",d:"\u10D3\u10E6\u10D4\u10E1",dd:"%d \u10D3\u10E6\u10D8\u10E1 \u10D2\u10D0\u10DC\u10DB\u10D0\u10D5\u10DA\u10DD\u10D1\u10D0\u10E8\u10D8",M:"\u10D7\u10D5\u10D8\u10E1",MM:"%d \u10D7\u10D5\u10D8\u10E1",y:"\u10EC\u10D4\u10DA\u10D8",yy:"%d \u10EC\u10DA\u10D8\u10E1"},ordinal:function(e){return e}};return r.default.locale(i,null,!0),i})});var wn=H((yt,Yt)=>{(function(n,t){typeof yt=="object"&&typeof Yt<"u"?Yt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_km=t(n.dayjs)})(yt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"km",weekdays:"\u17A2\u17B6\u1791\u17B7\u178F\u17D2\u1799_\u1785\u17D0\u1793\u17D2\u1791_\u17A2\u1784\u17D2\u1782\u17B6\u179A_\u1796\u17BB\u1792_\u1796\u17D2\u179A\u17A0\u179F\u17D2\u1794\u178F\u17B7\u17CD_\u179F\u17BB\u1780\u17D2\u179A_\u179F\u17C5\u179A\u17CD".split("_"),months:"\u1798\u1780\u179A\u17B6_\u1780\u17BB\u1798\u17D2\u1797\u17C8_\u1798\u17B8\u1793\u17B6_\u1798\u17C1\u179F\u17B6_\u17A7\u179F\u1797\u17B6_\u1798\u17B7\u1790\u17BB\u1793\u17B6_\u1780\u1780\u17D2\u1780\u178A\u17B6_\u179F\u17B8\u17A0\u17B6_\u1780\u1789\u17D2\u1789\u17B6_\u178F\u17BB\u179B\u17B6_\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6_\u1792\u17D2\u1793\u17BC".split("_"),weekStart:1,weekdaysShort:"\u17A2\u17B6_\u1785_\u17A2_\u1796_\u1796\u17D2\u179A_\u179F\u17BB_\u179F".split("_"),monthsShort:"\u1798\u1780\u179A\u17B6_\u1780\u17BB\u1798\u17D2\u1797\u17C8_\u1798\u17B8\u1793\u17B6_\u1798\u17C1\u179F\u17B6_\u17A7\u179F\u1797\u17B6_\u1798\u17B7\u1790\u17BB\u1793\u17B6_\u1780\u1780\u17D2\u1780\u178A\u17B6_\u179F\u17B8\u17A0\u17B6_\u1780\u1789\u17D2\u1789\u17B6_\u178F\u17BB\u179B\u17B6_\u179C\u17B7\u1785\u17D2\u1786\u17B7\u1780\u17B6_\u1792\u17D2\u1793\u17BC".split("_"),weekdaysMin:"\u17A2\u17B6_\u1785_\u17A2_\u1796_\u1796\u17D2\u179A_\u179F\u17BB_\u179F".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},relativeTime:{future:"%s\u1791\u17C0\u178F",past:"%s\u1798\u17BB\u1793",s:"\u1794\u17C9\u17BB\u1793\u17D2\u1798\u17B6\u1793\u179C\u17B7\u1793\u17B6\u1791\u17B8",m:"\u1798\u17BD\u1799\u1793\u17B6\u1791\u17B8",mm:"%d \u1793\u17B6\u1791\u17B8",h:"\u1798\u17BD\u1799\u1798\u17C9\u17C4\u1784",hh:"%d \u1798\u17C9\u17C4\u1784",d:"\u1798\u17BD\u1799\u1790\u17D2\u1784\u17C3",dd:"%d \u1790\u17D2\u1784\u17C3",M:"\u1798\u17BD\u1799\u1781\u17C2",MM:"%d \u1781\u17C2",y:"\u1798\u17BD\u1799\u1786\u17D2\u1793\u17B6\u17C6",yy:"%d \u1786\u17D2\u1793\u17B6\u17C6"}};return r.default.locale(i,null,!0),i})});var Cn=H((he,$n)=>{(function(n,t){typeof he=="object"&&typeof $n<"u"?t(he,w()):typeof define=="function"&&define.amd?define(["exports","dayjs"],t):t((n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ku={},n.dayjs)})(he,function(n,t){"use strict";function r(d){return d&&typeof d=="object"&&"default"in d?d:{default:d}}var i=r(t),e={1:"\u0661",2:"\u0662",3:"\u0663",4:"\u0664",5:"\u0665",6:"\u0666",7:"\u0667",8:"\u0668",9:"\u0669",0:"\u0660"},u={"\u0661":"1","\u0662":"2","\u0663":"3","\u0664":"4","\u0665":"5","\u0666":"6","\u0667":"7","\u0668":"8","\u0669":"9","\u0660":"0"},s=["\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u0634\u0648\u0628\u0627\u062A","\u0626\u0627\u062F\u0627\u0631","\u0646\u06CC\u0633\u0627\u0646","\u0626\u0627\u06CC\u0627\u0631","\u062D\u0648\u0632\u06D5\u06CC\u0631\u0627\u0646","\u062A\u06D5\u0645\u0645\u0648\u0648\u0632","\u0626\u0627\u0628","\u0626\u06D5\u06CC\u0644\u0648\u0648\u0644","\u062A\u0634\u0631\u06CC\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645","\u062A\u0634\u0631\u06CC\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645","\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645"],a={name:"ku",months:s,monthsShort:s,weekdays:"\u06CC\u06D5\u06A9\u0634\u06D5\u0645\u0645\u06D5_\u062F\u0648\u0648\u0634\u06D5\u0645\u0645\u06D5_\u0633\u06CE\u0634\u06D5\u0645\u0645\u06D5_\u0686\u0648\u0627\u0631\u0634\u06D5\u0645\u0645\u06D5_\u067E\u06CE\u0646\u062C\u0634\u06D5\u0645\u0645\u06D5_\u0647\u06D5\u06CC\u0646\u06CC_\u0634\u06D5\u0645\u0645\u06D5".split("_"),weekdaysShort:"\u06CC\u06D5\u06A9\u0634\u06D5\u0645_\u062F\u0648\u0648\u0634\u06D5\u0645_\u0633\u06CE\u0634\u06D5\u0645_\u0686\u0648\u0627\u0631\u0634\u06D5\u0645_\u067E\u06CE\u0646\u062C\u0634\u06D5\u0645_\u0647\u06D5\u06CC\u0646\u06CC_\u0634\u06D5\u0645\u0645\u06D5".split("_"),weekStart:6,weekdaysMin:"\u06CC_\u062F_\u0633_\u0686_\u067E_\u0647\u0640_\u0634".split("_"),preparse:function(d){return d.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(_){return u[_]}).replace(/،/g,",")},postformat:function(d){return d.replace(/\d/g,function(_){return e[_]}).replace(/,/g,"\u060C")},ordinal:function(d){return d},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiem:function(d){return d<12?"\u067E.\u0646":"\u062F.\u0646"},relativeTime:{future:"\u0644\u06D5 %s",past:"\u0644\u06D5\u0645\u06D5\u0648\u067E\u06CE\u0634 %s",s:"\u0686\u06D5\u0646\u062F \u0686\u0631\u06A9\u06D5\u06CC\u06D5\u06A9",m:"\u06CC\u06D5\u06A9 \u062E\u0648\u0644\u06D5\u06A9",mm:"%d \u062E\u0648\u0644\u06D5\u06A9",h:"\u06CC\u06D5\u06A9 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",hh:"%d \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",d:"\u06CC\u06D5\u06A9 \u0695\u06C6\u0698",dd:"%d \u0695\u06C6\u0698",M:"\u06CC\u06D5\u06A9 \u0645\u0627\u0646\u06AF",MM:"%d \u0645\u0627\u0646\u06AF",y:"\u06CC\u06D5\u06A9 \u0633\u0627\u06B5",yy:"%d \u0633\u0627\u06B5"}};i.default.locale(a,null,!0),n.default=a,n.englishToArabicNumbersMap=e,Object.defineProperty(n,"__esModule",{value:!0})})});var On=H((pt,Dt)=>{(function(n,t){typeof pt=="object"&&typeof Dt<"u"?Dt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ms=t(n.dayjs)})(pt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"ms",weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekStart:1,formats:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH.mm",LLLL:"dddd, D MMMM YYYY HH.mm"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},ordinal:function(e){return e+"."}};return r.default.locale(i,null,!0),i})});var zn=H((Lt,vt)=>{(function(n,t){typeof Lt=="object"&&typeof vt<"u"?vt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_my=t(n.dayjs)})(Lt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"my",weekdays:"\u1010\u1014\u1004\u103A\u1039\u1002\u1014\u103D\u1031_\u1010\u1014\u1004\u103A\u1039\u101C\u102C_\u1021\u1004\u103A\u1039\u1002\u102B_\u1017\u102F\u1012\u1039\u1013\u101F\u1030\u1038_\u1000\u103C\u102C\u101E\u1015\u1010\u1031\u1038_\u101E\u1031\u102C\u1000\u103C\u102C_\u1005\u1014\u1031".split("_"),months:"\u1007\u1014\u103A\u1014\u101D\u102B\u101B\u102E_\u1016\u1031\u1016\u1031\u102C\u103A\u101D\u102B\u101B\u102E_\u1019\u1010\u103A_\u1027\u1015\u103C\u102E_\u1019\u1031_\u1007\u103D\u1014\u103A_\u1007\u1030\u101C\u102D\u102F\u1004\u103A_\u101E\u103C\u1002\u102F\u1010\u103A_\u1005\u1000\u103A\u1010\u1004\u103A\u1018\u102C_\u1021\u1031\u102C\u1000\u103A\u1010\u102D\u102F\u1018\u102C_\u1014\u102D\u102F\u101D\u1004\u103A\u1018\u102C_\u1012\u102E\u1007\u1004\u103A\u1018\u102C".split("_"),weekStart:1,weekdaysShort:"\u1014\u103D\u1031_\u101C\u102C_\u1002\u102B_\u101F\u1030\u1038_\u1000\u103C\u102C_\u101E\u1031\u102C_\u1014\u1031".split("_"),monthsShort:"\u1007\u1014\u103A_\u1016\u1031_\u1019\u1010\u103A_\u1015\u103C\u102E_\u1019\u1031_\u1007\u103D\u1014\u103A_\u101C\u102D\u102F\u1004\u103A_\u101E\u103C_\u1005\u1000\u103A_\u1021\u1031\u102C\u1000\u103A_\u1014\u102D\u102F_\u1012\u102E".split("_"),weekdaysMin:"\u1014\u103D\u1031_\u101C\u102C_\u1002\u102B_\u101F\u1030\u1038_\u1000\u103C\u102C_\u101E\u1031\u102C_\u1014\u1031".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"\u101C\u102C\u1019\u100A\u103A\u1037 %s \u1019\u103E\u102C",past:"\u101C\u103D\u1014\u103A\u1001\u1032\u1037\u101E\u1031\u102C %s \u1000",s:"\u1005\u1000\u1039\u1000\u1014\u103A.\u1021\u1014\u100A\u103A\u1038\u1004\u101A\u103A",m:"\u1010\u1005\u103A\u1019\u102D\u1014\u1005\u103A",mm:"%d \u1019\u102D\u1014\u1005\u103A",h:"\u1010\u1005\u103A\u1014\u102C\u101B\u102E",hh:"%d \u1014\u102C\u101B\u102E",d:"\u1010\u1005\u103A\u101B\u1000\u103A",dd:"%d \u101B\u1000\u103A",M:"\u1010\u1005\u103A\u101C",MM:"%d \u101C",y:"\u1010\u1005\u103A\u1014\u103E\u1005\u103A",yy:"%d \u1014\u103E\u1005\u103A"}};return r.default.locale(i,null,!0),i})});var An=H((gt,St)=>{(function(n,t){typeof gt=="object"&&typeof St<"u"?St.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_nl=t(n.dayjs)})(gt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"nl",weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),ordinal:function(e){return"["+e+(e===1||e===8||e>=20?"ste":"de")+"]"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",m:"een minuut",mm:"%d minuten",h:"een uur",hh:"%d uur",d:"een dag",dd:"%d dagen",M:"een maand",MM:"%d maanden",y:"een jaar",yy:"%d jaar"}};return r.default.locale(i,null,!0),i})});var In=H((bt,kt)=>{(function(n,t){typeof bt=="object"&&typeof kt<"u"?kt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_pl=t(n.dayjs)})(bt,function(n){"use strict";function t(f){return f&&typeof f=="object"&&"default"in f?f:{default:f}}var r=t(n);function i(f){return f%10<5&&f%10>1&&~~(f/10)%10!=1}function e(f,y,l){var o=f+" ";switch(l){case"m":return y?"minuta":"minut\u0119";case"mm":return o+(i(f)?"minuty":"minut");case"h":return y?"godzina":"godzin\u0119";case"hh":return o+(i(f)?"godziny":"godzin");case"MM":return o+(i(f)?"miesi\u0105ce":"miesi\u0119cy");case"yy":return o+(i(f)?"lata":"lat")}}var u="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_wrze\u015Bnia_pa\u017Adziernika_listopada_grudnia".split("_"),s="stycze\u0144_luty_marzec_kwiecie\u0144_maj_czerwiec_lipiec_sierpie\u0144_wrzesie\u0144_pa\u017Adziernik_listopad_grudzie\u0144".split("_"),a=/D MMMM/,d=function(f,y){return a.test(y)?u[f.month()]:s[f.month()]};d.s=s,d.f=u;var _={name:"pl",weekdays:"niedziela_poniedzia\u0142ek_wtorek_\u015Broda_czwartek_pi\u0105tek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_\u015Br_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_\u015Ar_Cz_Pt_So".split("_"),months:d,monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_pa\u017A_lis_gru".split("_"),ordinal:function(f){return f+"."},weekStart:1,yearStart:4,relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",m:e,mm:e,h:e,hh:e,d:"1 dzie\u0144",dd:"%d dni",M:"miesi\u0105c",MM:e,y:"rok",yy:e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"}};return r.default.locale(_,null,!0),_})});var xn=H((Ht,Tt)=>{(function(n,t){typeof Ht=="object"&&typeof Tt<"u"?Tt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_pt_br=t(n.dayjs)})(Ht,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"pt-br",weekdays:"domingo_segunda-feira_ter\xE7a-feira_quarta-feira_quinta-feira_sexta-feira_s\xE1bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_s\xE1b".split("_"),weekdaysMin:"Do_2\xAA_3\xAA_4\xAA_5\xAA_6\xAA_S\xE1".split("_"),months:"janeiro_fevereiro_mar\xE7o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),ordinal:function(e){return e+"\xBA"},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [\xE0s] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [\xE0s] HH:mm"},relativeTime:{future:"em %s",past:"h\xE1 %s",s:"poucos segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um m\xEAs",MM:"%d meses",y:"um ano",yy:"%d anos"}};return r.default.locale(i,null,!0),i})});var qn=H((jt,wt)=>{(function(n,t){typeof jt=="object"&&typeof wt<"u"?wt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_pt=t(n.dayjs)})(jt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"pt",weekdays:"domingo_segunda-feira_ter\xE7a-feira_quarta-feira_quinta-feira_sexta-feira_s\xE1bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sab".split("_"),weekdaysMin:"Do_2\xAA_3\xAA_4\xAA_5\xAA_6\xAA_Sa".split("_"),months:"janeiro_fevereiro_mar\xE7o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),ordinal:function(e){return e+"\xBA"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [\xE0s] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [\xE0s] HH:mm"},relativeTime:{future:"em %s",past:"h\xE1 %s",s:"alguns segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um m\xEAs",MM:"%d meses",y:"um ano",yy:"%d anos"}};return r.default.locale(i,null,!0),i})});var Nn=H(($t,Ct)=>{(function(n,t){typeof $t=="object"&&typeof Ct<"u"?Ct.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ro=t(n.dayjs)})($t,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"ro",weekdays:"Duminic\u0103_Luni_Mar\u021Bi_Miercuri_Joi_Vineri_S\xE2mb\u0103t\u0103".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_S\xE2m".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_S\xE2".split("_"),months:"Ianuarie_Februarie_Martie_Aprilie_Mai_Iunie_Iulie_August_Septembrie_Octombrie_Noiembrie_Decembrie".split("_"),monthsShort:"Ian._Febr._Mart._Apr._Mai_Iun._Iul._Aug._Sept._Oct._Nov._Dec.".split("_"),weekStart:1,formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},relativeTime:{future:"peste %s",past:"acum %s",s:"c\xE2teva secunde",m:"un minut",mm:"%d minute",h:"o or\u0103",hh:"%d ore",d:"o zi",dd:"%d zile",M:"o lun\u0103",MM:"%d luni",y:"un an",yy:"%d ani"},ordinal:function(e){return e}};return r.default.locale(i,null,!0),i})});var En=H((Ot,zt)=>{(function(n,t){typeof Ot=="object"&&typeof zt<"u"?zt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_ru=t(n.dayjs)})(Ot,function(n){"use strict";function t(l){return l&&typeof l=="object"&&"default"in l?l:{default:l}}var r=t(n),i="\u044F\u043D\u0432\u0430\u0440\u044F_\u0444\u0435\u0432\u0440\u0430\u043B\u044F_\u043C\u0430\u0440\u0442\u0430_\u0430\u043F\u0440\u0435\u043B\u044F_\u043C\u0430\u044F_\u0438\u044E\u043D\u044F_\u0438\u044E\u043B\u044F_\u0430\u0432\u0433\u0443\u0441\u0442\u0430_\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F_\u043E\u043A\u0442\u044F\u0431\u0440\u044F_\u043D\u043E\u044F\u0431\u0440\u044F_\u0434\u0435\u043A\u0430\u0431\u0440\u044F".split("_"),e="\u044F\u043D\u0432\u0430\u0440\u044C_\u0444\u0435\u0432\u0440\u0430\u043B\u044C_\u043C\u0430\u0440\u0442_\u0430\u043F\u0440\u0435\u043B\u044C_\u043C\u0430\u0439_\u0438\u044E\u043D\u044C_\u0438\u044E\u043B\u044C_\u0430\u0432\u0433\u0443\u0441\u0442_\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C_\u043E\u043A\u0442\u044F\u0431\u0440\u044C_\u043D\u043E\u044F\u0431\u0440\u044C_\u0434\u0435\u043A\u0430\u0431\u0440\u044C".split("_"),u="\u044F\u043D\u0432._\u0444\u0435\u0432\u0440._\u043C\u0430\u0440._\u0430\u043F\u0440._\u043C\u0430\u044F_\u0438\u044E\u043D\u044F_\u0438\u044E\u043B\u044F_\u0430\u0432\u0433._\u0441\u0435\u043D\u0442._\u043E\u043A\u0442._\u043D\u043E\u044F\u0431._\u0434\u0435\u043A.".split("_"),s="\u044F\u043D\u0432._\u0444\u0435\u0432\u0440._\u043C\u0430\u0440\u0442_\u0430\u043F\u0440._\u043C\u0430\u0439_\u0438\u044E\u043D\u044C_\u0438\u044E\u043B\u044C_\u0430\u0432\u0433._\u0441\u0435\u043D\u0442._\u043E\u043A\u0442._\u043D\u043E\u044F\u0431._\u0434\u0435\u043A.".split("_"),a=/D[oD]?(\[[^[\]]*\]|\s)+MMMM?/;function d(l,o,c){var D,p;return c==="m"?o?"\u043C\u0438\u043D\u0443\u0442\u0430":"\u043C\u0438\u043D\u0443\u0442\u0443":l+" "+(D=+l,p={mm:o?"\u043C\u0438\u043D\u0443\u0442\u0430_\u043C\u0438\u043D\u0443\u0442\u044B_\u043C\u0438\u043D\u0443\u0442":"\u043C\u0438\u043D\u0443\u0442\u0443_\u043C\u0438\u043D\u0443\u0442\u044B_\u043C\u0438\u043D\u0443\u0442",hh:"\u0447\u0430\u0441_\u0447\u0430\u0441\u0430_\u0447\u0430\u0441\u043E\u0432",dd:"\u0434\u0435\u043D\u044C_\u0434\u043D\u044F_\u0434\u043D\u0435\u0439",MM:"\u043C\u0435\u0441\u044F\u0446_\u043C\u0435\u0441\u044F\u0446\u0430_\u043C\u0435\u0441\u044F\u0446\u0435\u0432",yy:"\u0433\u043E\u0434_\u0433\u043E\u0434\u0430_\u043B\u0435\u0442"}[c].split("_"),D%10==1&&D%100!=11?p[0]:D%10>=2&&D%10<=4&&(D%100<10||D%100>=20)?p[1]:p[2])}var _=function(l,o){return a.test(o)?i[l.month()]:e[l.month()]};_.s=e,_.f=i;var f=function(l,o){return a.test(o)?u[l.month()]:s[l.month()]};f.s=s,f.f=u;var y={name:"ru",weekdays:"\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435_\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A_\u0432\u0442\u043E\u0440\u043D\u0438\u043A_\u0441\u0440\u0435\u0434\u0430_\u0447\u0435\u0442\u0432\u0435\u0440\u0433_\u043F\u044F\u0442\u043D\u0438\u0446\u0430_\u0441\u0443\u0431\u0431\u043E\u0442\u0430".split("_"),weekdaysShort:"\u0432\u0441\u043A_\u043F\u043D\u0434_\u0432\u0442\u0440_\u0441\u0440\u0434_\u0447\u0442\u0432_\u043F\u0442\u043D_\u0441\u0431\u0442".split("_"),weekdaysMin:"\u0432\u0441_\u043F\u043D_\u0432\u0442_\u0441\u0440_\u0447\u0442_\u043F\u0442_\u0441\u0431".split("_"),months:_,monthsShort:f,weekStart:1,yearStart:4,formats:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY \u0433.",LLL:"D MMMM YYYY \u0433., H:mm",LLLL:"dddd, D MMMM YYYY \u0433., H:mm"},relativeTime:{future:"\u0447\u0435\u0440\u0435\u0437 %s",past:"%s \u043D\u0430\u0437\u0430\u0434",s:"\u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E \u0441\u0435\u043A\u0443\u043D\u0434",m:d,mm:d,h:"\u0447\u0430\u0441",hh:d,d:"\u0434\u0435\u043D\u044C",dd:d,M:"\u043C\u0435\u0441\u044F\u0446",MM:d,y:"\u0433\u043E\u0434",yy:d},ordinal:function(l){return l},meridiem:function(l){return l<4?"\u043D\u043E\u0447\u0438":l<12?"\u0443\u0442\u0440\u0430":l<17?"\u0434\u043D\u044F":"\u0432\u0435\u0447\u0435\u0440\u0430"}};return r.default.locale(y,null,!0),y})});var Fn=H((At,It)=>{(function(n,t){typeof At=="object"&&typeof It<"u"?It.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_sv=t(n.dayjs)})(At,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"sv",weekdays:"s\xF6ndag_m\xE5ndag_tisdag_onsdag_torsdag_fredag_l\xF6rdag".split("_"),weekdaysShort:"s\xF6n_m\xE5n_tis_ons_tor_fre_l\xF6r".split("_"),weekdaysMin:"s\xF6_m\xE5_ti_on_to_fr_l\xF6".split("_"),months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekStart:1,yearStart:4,ordinal:function(e){var u=e%10;return"["+e+(u===1||u===2?"a":"e")+"]"},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},relativeTime:{future:"om %s",past:"f\xF6r %s sedan",s:"n\xE5gra sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en m\xE5nad",MM:"%d m\xE5nader",y:"ett \xE5r",yy:"%d \xE5r"}};return r.default.locale(i,null,!0),i})});var Jn=H((xt,qt)=>{(function(n,t){typeof xt=="object"&&typeof qt<"u"?qt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_tr=t(n.dayjs)})(xt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"tr",weekdays:"Pazar_Pazartesi_Sal\u0131_\xC7ar\u015Famba_Per\u015Fembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pts_Sal_\xC7ar_Per_Cum_Cts".split("_"),weekdaysMin:"Pz_Pt_Sa_\xC7a_Pe_Cu_Ct".split("_"),months:"Ocak_\u015Eubat_Mart_Nisan_May\u0131s_Haziran_Temmuz_A\u011Fustos_Eyl\xFCl_Ekim_Kas\u0131m_Aral\u0131k".split("_"),monthsShort:"Oca_\u015Eub_Mar_Nis_May_Haz_Tem_A\u011Fu_Eyl_Eki_Kas_Ara".split("_"),weekStart:1,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},relativeTime:{future:"%s sonra",past:"%s \xF6nce",s:"birka\xE7 saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir g\xFCn",dd:"%d g\xFCn",M:"bir ay",MM:"%d ay",y:"bir y\u0131l",yy:"%d y\u0131l"},ordinal:function(e){return e+"."}};return r.default.locale(i,null,!0),i})});var Un=H((Nt,Et)=>{(function(n,t){typeof Nt=="object"&&typeof Et<"u"?Et.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_uk=t(n.dayjs)})(Nt,function(n){"use strict";function t(_){return _&&typeof _=="object"&&"default"in _?_:{default:_}}var r=t(n),i="\u0441\u0456\u0447\u043D\u044F_\u043B\u044E\u0442\u043E\u0433\u043E_\u0431\u0435\u0440\u0435\u0437\u043D\u044F_\u043A\u0432\u0456\u0442\u043D\u044F_\u0442\u0440\u0430\u0432\u043D\u044F_\u0447\u0435\u0440\u0432\u043D\u044F_\u043B\u0438\u043F\u043D\u044F_\u0441\u0435\u0440\u043F\u043D\u044F_\u0432\u0435\u0440\u0435\u0441\u043D\u044F_\u0436\u043E\u0432\u0442\u043D\u044F_\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434\u0430_\u0433\u0440\u0443\u0434\u043D\u044F".split("_"),e="\u0441\u0456\u0447\u0435\u043D\u044C_\u043B\u044E\u0442\u0438\u0439_\u0431\u0435\u0440\u0435\u0437\u0435\u043D\u044C_\u043A\u0432\u0456\u0442\u0435\u043D\u044C_\u0442\u0440\u0430\u0432\u0435\u043D\u044C_\u0447\u0435\u0440\u0432\u0435\u043D\u044C_\u043B\u0438\u043F\u0435\u043D\u044C_\u0441\u0435\u0440\u043F\u0435\u043D\u044C_\u0432\u0435\u0440\u0435\u0441\u0435\u043D\u044C_\u0436\u043E\u0432\u0442\u0435\u043D\u044C_\u043B\u0438\u0441\u0442\u043E\u043F\u0430\u0434_\u0433\u0440\u0443\u0434\u0435\u043D\u044C".split("_"),u=/D[oD]?(\[[^[\]]*\]|\s)+MMMM?/;function s(_,f,y){var l,o;return y==="m"?f?"\u0445\u0432\u0438\u043B\u0438\u043D\u0430":"\u0445\u0432\u0438\u043B\u0438\u043D\u0443":y==="h"?f?"\u0433\u043E\u0434\u0438\u043D\u0430":"\u0433\u043E\u0434\u0438\u043D\u0443":_+" "+(l=+_,o={ss:f?"\u0441\u0435\u043A\u0443\u043D\u0434\u0430_\u0441\u0435\u043A\u0443\u043D\u0434\u0438_\u0441\u0435\u043A\u0443\u043D\u0434":"\u0441\u0435\u043A\u0443\u043D\u0434\u0443_\u0441\u0435\u043A\u0443\u043D\u0434\u0438_\u0441\u0435\u043A\u0443\u043D\u0434",mm:f?"\u0445\u0432\u0438\u043B\u0438\u043D\u0430_\u0445\u0432\u0438\u043B\u0438\u043D\u0438_\u0445\u0432\u0438\u043B\u0438\u043D":"\u0445\u0432\u0438\u043B\u0438\u043D\u0443_\u0445\u0432\u0438\u043B\u0438\u043D\u0438_\u0445\u0432\u0438\u043B\u0438\u043D",hh:f?"\u0433\u043E\u0434\u0438\u043D\u0430_\u0433\u043E\u0434\u0438\u043D\u0438_\u0433\u043E\u0434\u0438\u043D":"\u0433\u043E\u0434\u0438\u043D\u0443_\u0433\u043E\u0434\u0438\u043D\u0438_\u0433\u043E\u0434\u0438\u043D",dd:"\u0434\u0435\u043D\u044C_\u0434\u043D\u0456_\u0434\u043D\u0456\u0432",MM:"\u043C\u0456\u0441\u044F\u0446\u044C_\u043C\u0456\u0441\u044F\u0446\u0456_\u043C\u0456\u0441\u044F\u0446\u0456\u0432",yy:"\u0440\u0456\u043A_\u0440\u043E\u043A\u0438_\u0440\u043E\u043A\u0456\u0432"}[y].split("_"),l%10==1&&l%100!=11?o[0]:l%10>=2&&l%10<=4&&(l%100<10||l%100>=20)?o[1]:o[2])}var a=function(_,f){return u.test(f)?i[_.month()]:e[_.month()]};a.s=e,a.f=i;var d={name:"uk",weekdays:"\u043D\u0435\u0434\u0456\u043B\u044F_\u043F\u043E\u043D\u0435\u0434\u0456\u043B\u043E\u043A_\u0432\u0456\u0432\u0442\u043E\u0440\u043E\u043A_\u0441\u0435\u0440\u0435\u0434\u0430_\u0447\u0435\u0442\u0432\u0435\u0440_\u043F\u2019\u044F\u0442\u043D\u0438\u0446\u044F_\u0441\u0443\u0431\u043E\u0442\u0430".split("_"),weekdaysShort:"\u043D\u0434\u043B_\u043F\u043D\u0434_\u0432\u0442\u0440_\u0441\u0440\u0434_\u0447\u0442\u0432_\u043F\u0442\u043D_\u0441\u0431\u0442".split("_"),weekdaysMin:"\u043D\u0434_\u043F\u043D_\u0432\u0442_\u0441\u0440_\u0447\u0442_\u043F\u0442_\u0441\u0431".split("_"),months:a,monthsShort:"\u0441\u0456\u0447_\u043B\u044E\u0442_\u0431\u0435\u0440_\u043A\u0432\u0456\u0442_\u0442\u0440\u0430\u0432_\u0447\u0435\u0440\u0432_\u043B\u0438\u043F_\u0441\u0435\u0440\u043F_\u0432\u0435\u0440_\u0436\u043E\u0432\u0442_\u043B\u0438\u0441\u0442_\u0433\u0440\u0443\u0434".split("_"),weekStart:1,relativeTime:{future:"\u0437\u0430 %s",past:"%s \u0442\u043E\u043C\u0443",s:"\u0434\u0435\u043A\u0456\u043B\u044C\u043A\u0430 \u0441\u0435\u043A\u0443\u043D\u0434",m:s,mm:s,h:s,hh:s,d:"\u0434\u0435\u043D\u044C",dd:s,M:"\u043C\u0456\u0441\u044F\u0446\u044C",MM:s,y:"\u0440\u0456\u043A",yy:s},ordinal:function(_){return _},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY \u0440.",LLL:"D MMMM YYYY \u0440., HH:mm",LLLL:"dddd, D MMMM YYYY \u0440., HH:mm"}};return r.default.locale(d,null,!0),d})});var Wn=H((Ft,Jt)=>{(function(n,t){typeof Ft=="object"&&typeof Jt<"u"?Jt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_vi=t(n.dayjs)})(Ft,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"vi",weekdays:"ch\u1EE7 nh\u1EADt_th\u1EE9 hai_th\u1EE9 ba_th\u1EE9 t\u01B0_th\u1EE9 n\u0103m_th\u1EE9 s\xE1u_th\u1EE9 b\u1EA3y".split("_"),months:"th\xE1ng 1_th\xE1ng 2_th\xE1ng 3_th\xE1ng 4_th\xE1ng 5_th\xE1ng 6_th\xE1ng 7_th\xE1ng 8_th\xE1ng 9_th\xE1ng 10_th\xE1ng 11_th\xE1ng 12".split("_"),weekStart:1,weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),monthsShort:"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),ordinal:function(e){return e},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [n\u0103m] YYYY",LLL:"D MMMM [n\u0103m] YYYY HH:mm",LLLL:"dddd, D MMMM [n\u0103m] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},relativeTime:{future:"%s t\u1EDBi",past:"%s tr\u01B0\u1EDBc",s:"v\xE0i gi\xE2y",m:"m\u1ED9t ph\xFAt",mm:"%d ph\xFAt",h:"m\u1ED9t gi\u1EDD",hh:"%d gi\u1EDD",d:"m\u1ED9t ng\xE0y",dd:"%d ng\xE0y",M:"m\u1ED9t th\xE1ng",MM:"%d th\xE1ng",y:"m\u1ED9t n\u0103m",yy:"%d n\u0103m"}};return r.default.locale(i,null,!0),i})});var Pn=H((Ut,Wt)=>{(function(n,t){typeof Ut=="object"&&typeof Wt<"u"?Wt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_zh_cn=t(n.dayjs)})(Ut,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"zh-cn",weekdays:"\u661F\u671F\u65E5_\u661F\u671F\u4E00_\u661F\u671F\u4E8C_\u661F\u671F\u4E09_\u661F\u671F\u56DB_\u661F\u671F\u4E94_\u661F\u671F\u516D".split("_"),weekdaysShort:"\u5468\u65E5_\u5468\u4E00_\u5468\u4E8C_\u5468\u4E09_\u5468\u56DB_\u5468\u4E94_\u5468\u516D".split("_"),weekdaysMin:"\u65E5_\u4E00_\u4E8C_\u4E09_\u56DB_\u4E94_\u516D".split("_"),months:"\u4E00\u6708_\u4E8C\u6708_\u4E09\u6708_\u56DB\u6708_\u4E94\u6708_\u516D\u6708_\u4E03\u6708_\u516B\u6708_\u4E5D\u6708_\u5341\u6708_\u5341\u4E00\u6708_\u5341\u4E8C\u6708".split("_"),monthsShort:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),ordinal:function(e,u){return u==="W"?e+"\u5468":e+"\u65E5"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY\u5E74M\u6708D\u65E5",LLL:"YYYY\u5E74M\u6708D\u65E5Ah\u70B9mm\u5206",LLLL:"YYYY\u5E74M\u6708D\u65E5ddddAh\u70B9mm\u5206",l:"YYYY/M/D",ll:"YYYY\u5E74M\u6708D\u65E5",lll:"YYYY\u5E74M\u6708D\u65E5 HH:mm",llll:"YYYY\u5E74M\u6708D\u65E5dddd HH:mm"},relativeTime:{future:"%s\u5185",past:"%s\u524D",s:"\u51E0\u79D2",m:"1 \u5206\u949F",mm:"%d \u5206\u949F",h:"1 \u5C0F\u65F6",hh:"%d \u5C0F\u65F6",d:"1 \u5929",dd:"%d \u5929",M:"1 \u4E2A\u6708",MM:"%d \u4E2A\u6708",y:"1 \u5E74",yy:"%d \u5E74"},meridiem:function(e,u){var s=100*e+u;return s<600?"\u51CC\u6668":s<900?"\u65E9\u4E0A":s<1100?"\u4E0A\u5348":s<1300?"\u4E2D\u5348":s<1800?"\u4E0B\u5348":"\u665A\u4E0A"}};return r.default.locale(i,null,!0),i})});var Rn=H((Pt,Rt)=>{(function(n,t){typeof Pt=="object"&&typeof Rt<"u"?Rt.exports=t(w()):typeof define=="function"&&define.amd?define(["dayjs"],t):(n=typeof globalThis<"u"?globalThis:n||self).dayjs_locale_zh_tw=t(n.dayjs)})(Pt,function(n){"use strict";function t(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var r=t(n),i={name:"zh-tw",weekdays:"\u661F\u671F\u65E5_\u661F\u671F\u4E00_\u661F\u671F\u4E8C_\u661F\u671F\u4E09_\u661F\u671F\u56DB_\u661F\u671F\u4E94_\u661F\u671F\u516D".split("_"),weekdaysShort:"\u9031\u65E5_\u9031\u4E00_\u9031\u4E8C_\u9031\u4E09_\u9031\u56DB_\u9031\u4E94_\u9031\u516D".split("_"),weekdaysMin:"\u65E5_\u4E00_\u4E8C_\u4E09_\u56DB_\u4E94_\u516D".split("_"),months:"\u4E00\u6708_\u4E8C\u6708_\u4E09\u6708_\u56DB\u6708_\u4E94\u6708_\u516D\u6708_\u4E03\u6708_\u516B\u6708_\u4E5D\u6708_\u5341\u6708_\u5341\u4E00\u6708_\u5341\u4E8C\u6708".split("_"),monthsShort:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),ordinal:function(e,u){return u==="W"?e+"\u9031":e+"\u65E5"},formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY\u5E74M\u6708D\u65E5",LLL:"YYYY\u5E74M\u6708D\u65E5 HH:mm",LLLL:"YYYY\u5E74M\u6708D\u65E5dddd HH:mm",l:"YYYY/M/D",ll:"YYYY\u5E74M\u6708D\u65E5",lll:"YYYY\u5E74M\u6708D\u65E5 HH:mm",llll:"YYYY\u5E74M\u6708D\u65E5dddd HH:mm"},relativeTime:{future:"%s\u5167",past:"%s\u524D",s:"\u5E7E\u79D2",m:"1 \u5206\u9418",mm:"%d \u5206\u9418",h:"1 \u5C0F\u6642",hh:"%d \u5C0F\u6642",d:"1 \u5929",dd:"%d \u5929",M:"1 \u500B\u6708",MM:"%d \u500B\u6708",y:"1 \u5E74",yy:"%d \u5E74"},meridiem:function(e,u){var s=100*e+u;return s<600?"\u51CC\u6668":s<900?"\u65E9\u4E0A":s<1100?"\u4E0A\u5348":s<1300?"\u4E2D\u5348":s<1800?"\u4E0B\u5348":"\u665A\u4E0A"}};return r.default.locale(i,null,!0),i})});var Vt=60,Gt=Vt*60,Kt=Gt*24,ri=Kt*7,se=1e3,fe=Vt*se,pe=Gt*se,Bt=Kt*se,Xt=ri*se,oe="millisecond",te="second",ne="minute",ie="hour",G="day",ue="week",P="month",le="quarter",K="year",re="date",Qt="YYYY-MM-DDTHH:mm:ssZ",De="Invalid Date",en=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,tn=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;var rn={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var r=["th","st","nd","rd"],i=t%100;return"["+t+(r[(i-20)%10]||r[i]||r[0])+"]"}};var Le=function(t,r,i){var e=String(t);return!e||e.length>=r?t:""+Array(r+1-e.length).join(i)+t},si=function(t){var r=-t.utcOffset(),i=Math.abs(r),e=Math.floor(i/60),u=i%60;return(r<=0?"+":"-")+Le(e,2,"0")+":"+Le(u,2,"0")},ai=function n(t,r){if(t.date()<r.date())return-n(r,t);var i=(r.year()-t.year())*12+(r.month()-t.month()),e=t.clone().add(i,P),u=r-e<0,s=t.clone().add(i+(u?-1:1),P);return+(-(i+(r-e)/(u?e-s:s-e))||0)},ui=function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},oi=function(t){var r={M:P,y:K,w:ue,d:G,D:re,h:ie,m:ne,s:te,ms:oe,Q:le};return r[t]||String(t||"").toLowerCase().replace(/s$/,"")},di=function(t){return t===void 0},sn={s:Le,z:si,m:ai,a:ui,p:oi,u:di};var de="en",ae={};ae[de]=rn;var ve=function(t){return t instanceof ce},me=function n(t,r,i){var e;if(!t)return de;if(typeof t=="string"){var u=t.toLowerCase();ae[u]&&(e=u),r&&(ae[u]=r,e=u);var s=t.split("-");if(!e&&s.length>1)return n(s[0])}else{var a=t.name;ae[a]=t,e=a}return!i&&e&&(de=e),e||!i&&de},J=function(t,r){if(ve(t))return t.clone();var i=typeof r=="object"?r:{};return i.date=t,i.args=arguments,new ce(i)},_i=function(t,r){return J(t,{locale:r.$L,utc:r.$u,x:r.$x,$offset:r.$offset})},z=sn;z.l=me;z.i=ve;z.w=_i;var fi=function(t){var r=t.date,i=t.utc;if(r===null)return new Date(NaN);if(z.u(r))return new Date;if(r instanceof Date)return new Date(r);if(typeof r=="string"&&!/Z$/i.test(r)){var e=r.match(en);if(e){var u=e[2]-1||0,s=(e[7]||"0").substring(0,3);return i?new Date(Date.UTC(e[1],u,e[3]||1,e[4]||0,e[5]||0,e[6]||0,s)):new Date(e[1],u,e[3]||1,e[4]||0,e[5]||0,e[6]||0,s)}}return new Date(r)},ce=function(){function n(r){this.$L=me(r.locale,null,!0),this.parse(r)}var t=n.prototype;return t.parse=function(i){this.$d=fi(i),this.$x=i.x||{},this.init()},t.init=function(){var i=this.$d;this.$y=i.getFullYear(),this.$M=i.getMonth(),this.$D=i.getDate(),this.$W=i.getDay(),this.$H=i.getHours(),this.$m=i.getMinutes(),this.$s=i.getSeconds(),this.$ms=i.getMilliseconds()},t.$utils=function(){return z},t.isValid=function(){return this.$d.toString()!==De},t.isSame=function(i,e){var u=J(i);return this.startOf(e)<=u&&u<=this.endOf(e)},t.isAfter=function(i,e){return J(i)<this.startOf(e)},t.isBefore=function(i,e){return this.endOf(e)<J(i)},t.$g=function(i,e,u){return z.u(i)?this[e]:this.set(u,i)},t.unix=function(){return Math.floor(this.valueOf()/1e3)},t.valueOf=function(){return this.$d.getTime()},t.startOf=function(i,e){var u=this,s=z.u(e)?!0:e,a=z.p(i),d=function(k,T){var S=z.w(u.$u?Date.UTC(u.$y,T,k):new Date(u.$y,T,k),u);return s?S:S.endOf(G)},_=function(k,T){var S=[0,0,0,0],C=[23,59,59,999];return z.w(u.toDate()[k].apply(u.toDate("s"),(s?S:C).slice(T)),u)},f=this.$W,y=this.$M,l=this.$D,o="set"+(this.$u?"UTC":"");switch(a){case K:return s?d(1,0):d(31,11);case P:return s?d(1,y):d(0,y+1);case ue:{var c=this.$locale().weekStart||0,D=(f<c?f+7:f)-c;return d(s?l-D:l+(6-D),y)}case G:case re:return _(o+"Hours",0);case ie:return _(o+"Minutes",1);case ne:return _(o+"Seconds",2);case te:return _(o+"Milliseconds",3);default:return this.clone()}},t.endOf=function(i){return this.startOf(i,!1)},t.$set=function(i,e){var u,s=z.p(i),a="set"+(this.$u?"UTC":""),d=(u={},u[G]=a+"Date",u[re]=a+"Date",u[P]=a+"Month",u[K]=a+"FullYear",u[ie]=a+"Hours",u[ne]=a+"Minutes",u[te]=a+"Seconds",u[oe]=a+"Milliseconds",u)[s],_=s===G?this.$D+(e-this.$W):e;if(s===P||s===K){var f=this.clone().set(re,1);f.$d[d](_),f.init(),this.$d=f.set(re,Math.min(this.$D,f.daysInMonth())).$d}else d&&this.$d[d](_);return this.init(),this},t.set=function(i,e){return this.clone().$set(i,e)},t.get=function(i){return this[z.p(i)]()},t.add=function(i,e){var u=this,s;i=Number(i);var a=z.p(e),d=function(l){var o=J(u);return z.w(o.date(o.date()+Math.round(l*i)),u)};if(a===P)return this.set(P,this.$M+i);if(a===K)return this.set(K,this.$y+i);if(a===G)return d(1);if(a===ue)return d(7);var _=(s={},s[ne]=fe,s[ie]=pe,s[te]=se,s)[a]||1,f=this.$d.getTime()+i*_;return z.w(f,this)},t.subtract=function(i,e){return this.add(i*-1,e)},t.format=function(i){var e=this,u=this.$locale();if(!this.isValid())return u.invalidDate||De;var s=i||Qt,a=z.z(this),d=this.$H,_=this.$m,f=this.$M,y=u.weekdays,l=u.months,o=u.meridiem,c=function(S,C,O,x){return S&&(S[C]||S(e,s))||O[C].slice(0,x)},D=function(S){return z.s(d%12||12,S,"0")},p=o||function(T,S,C){var O=T<12?"AM":"PM";return C?O.toLowerCase():O},k=function(S){switch(S){case"YY":return String(e.$y).slice(-2);case"YYYY":return z.s(e.$y,4,"0");case"M":return f+1;case"MM":return z.s(f+1,2,"0");case"MMM":return c(u.monthsShort,f,l,3);case"MMMM":return c(l,f);case"D":return e.$D;case"DD":return z.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return c(u.weekdaysMin,e.$W,y,2);case"ddd":return c(u.weekdaysShort,e.$W,y,3);case"dddd":return y[e.$W];case"H":return String(d);case"HH":return z.s(d,2,"0");case"h":return D(1);case"hh":return D(2);case"a":return p(d,_,!0);case"A":return p(d,_,!1);case"m":return String(_);case"mm":return z.s(_,2,"0");case"s":return String(e.$s);case"ss":return z.s(e.$s,2,"0");case"SSS":return z.s(e.$ms,3,"0");case"Z":return a;default:break}return null};return s.replace(tn,function(T,S){return S||k(T)||a.replace(":","")})},t.utcOffset=function(){return-Math.round(this.$d.getTimezoneOffset()/15)*15},t.diff=function(i,e,u){var s=this,a=z.p(e),d=J(i),_=(d.utcOffset()-this.utcOffset())*fe,f=this-d,y=function(){return z.m(s,d)},l;switch(a){case K:l=y()/12;break;case P:l=y();break;case le:l=y()/3;break;case ue:l=(f-_)/Xt;break;case G:l=(f-_)/Bt;break;case ie:l=f/pe;break;case ne:l=f/fe;break;case te:l=f/se;break;default:l=f;break}return u?l:z.a(l)},t.daysInMonth=function(){return this.endOf(P).$D},t.$locale=function(){return ae[this.$L]},t.locale=function(i,e){if(!i)return this.$L;var u=this.clone(),s=me(i,e,!0);return s&&(u.$L=s),u},t.clone=function(){return z.w(this.$d,this)},t.toDate=function(){return new Date(this.valueOf())},t.toJSON=function(){return this.isValid()?this.toISOString():null},t.toISOString=function(){return this.$d.toISOString()},t.toString=function(){return this.$d.toUTCString()},n}(),an=ce.prototype;J.prototype=an;[["$ms",oe],["$s",te],["$m",ne],["$H",ie],["$W",G],["$M",P],["$y",K],["$D",re]].forEach(function(n){an[n[1]]=function(t){return this.$g(t,n[0],n[1])}});J.extend=function(n,t){return n.$i||(n(t,ce,J),n.$i=!0),J};J.locale=me;J.isDayjs=ve;J.unix=function(n){return J(n*1e3)};J.en=ae[de];J.Ls=ae;J.p={};var A=J;var Vn=_e(un(),1),Gn=_e(on(),1),Kn=_e(dn(),1),Bn=_e(_n(),1);A.extend(Vn.default);A.extend(Gn.default);A.extend(Kn.default);A.extend(Bn.default);window.dayjs=A;function li({displayFormat:n,firstDayOfWeek:t,isAutofocused:r,locale:i,shouldCloseOnDateSelection:e,state:u}){let s=A.tz.guess();return{daysInFocusedMonth:[],displayText:"",emptyDaysInFocusedMonth:[],focusedDate:null,focusedMonth:null,focusedYear:null,hour:null,isClearingState:!1,minute:null,second:null,state:u,dayLabels:[],months:[],init:function(){A.locale(Zn[i]??Zn.en),this.focusedDate=A().tz(s);let a=this.getSelectedDate()??A().tz(s).hour(0).minute(0).second(0);(this.getMaxDate()!==null&&a.isAfter(this.getMaxDate())||this.getMinDate()!==null&&a.isBefore(this.getMinDate()))&&(a=null),this.hour=a?.hour()??0,this.minute=a?.minute()??0,this.second=a?.second()??0,this.setDisplayText(),this.setMonths(),this.setDayLabels(),r&&this.$nextTick(()=>this.togglePanelVisibility(this.$refs.button)),this.$watch("focusedMonth",()=>{this.focusedMonth=+this.focusedMonth,this.focusedDate.month()!==this.focusedMonth&&(this.focusedDate=this.focusedDate.month(this.focusedMonth))}),this.$watch("focusedYear",()=>{if(this.focusedYear?.length>4&&(this.focusedYear=this.focusedYear.substring(0,4)),!this.focusedYear||this.focusedYear?.length!==4)return;let d=+this.focusedYear;Number.isInteger(d)||(d=A().tz(s).year(),this.focusedYear=d),this.focusedDate.year()!==d&&(this.focusedDate=this.focusedDate.year(d))}),this.$watch("focusedDate",()=>{let d=this.focusedDate.month(),_=this.focusedDate.year();this.focusedMonth!==d&&(this.focusedMonth=d),this.focusedYear!==_&&(this.focusedYear=_),this.setupDaysGrid()}),this.$watch("hour",()=>{let d=+this.hour;if(Number.isInteger(d)?d>23?this.hour=0:d<0?this.hour=23:this.hour=d:this.hour=0,this.isClearingState)return;let _=this.getSelectedDate()??this.focusedDate;this.setState(_.hour(this.hour??0))}),this.$watch("minute",()=>{let d=+this.minute;if(Number.isInteger(d)?d>59?this.minute=0:d<0?this.minute=59:this.minute=d:this.minute=0,this.isClearingState)return;let _=this.getSelectedDate()??this.focusedDate;this.setState(_.minute(this.minute??0))}),this.$watch("second",()=>{let d=+this.second;if(Number.isInteger(d)?d>59?this.second=0:d<0?this.second=59:this.second=d:this.second=0,this.isClearingState)return;let _=this.getSelectedDate()??this.focusedDate;this.setState(_.second(this.second??0))}),this.$watch("state",()=>{if(this.state===void 0)return;let d=this.getSelectedDate();if(d===null){this.clearState();return}this.getMaxDate()!==null&&d?.isAfter(this.getMaxDate())&&(d=null),this.getMinDate()!==null&&d?.isBefore(this.getMinDate())&&(d=null);let _=d?.hour()??0;this.hour!==_&&(this.hour=_);let f=d?.minute()??0;this.minute!==f&&(this.minute=f);let y=d?.second()??0;this.second!==y&&(this.second=y),this.setDisplayText()})},clearState:function(){this.isClearingState=!0,this.setState(null),this.hour=0,this.minute=0,this.second=0,this.$nextTick(()=>this.isClearingState=!1)},dateIsDisabled:function(a){return!!(this.$refs?.disabledDates&&JSON.parse(this.$refs.disabledDates.value??[]).some(d=>(d=A(d),d.isValid()?d.isSame(a,"day"):!1))||this.getMaxDate()&&a.isAfter(this.getMaxDate(),"day")||this.getMinDate()&&a.isBefore(this.getMinDate(),"day"))},dayIsDisabled:function(a){return this.focusedDate??(this.focusedDate=A().tz(s)),this.dateIsDisabled(this.focusedDate.date(a))},dayIsSelected:function(a){let d=this.getSelectedDate();return d===null?!1:(this.focusedDate??(this.focusedDate=A().tz(s)),d.date()===a&&d.month()===this.focusedDate.month()&&d.year()===this.focusedDate.year())},dayIsToday:function(a){let d=A().tz(s);return this.focusedDate??(this.focusedDate=d),d.date()===a&&d.month()===this.focusedDate.month()&&d.year()===this.focusedDate.year()},focusPreviousDay:function(){this.focusedDate??(this.focusedDate=A().tz(s)),this.focusedDate=this.focusedDate.subtract(1,"day")},focusPreviousWeek:function(){this.focusedDate??(this.focusedDate=A().tz(s)),this.focusedDate=this.focusedDate.subtract(1,"week")},focusNextDay:function(){this.focusedDate??(this.focusedDate=A().tz(s)),this.focusedDate=this.focusedDate.add(1,"day")},focusNextWeek:function(){this.focusedDate??(this.focusedDate=A().tz(s)),this.focusedDate=this.focusedDate.add(1,"week")},getDayLabels:function(){let a=A.weekdaysShort();return t===0?a:[...a.slice(t),...a.slice(0,t)]},getMaxDate:function(){let a=A(this.$refs.maxDate?.value);return a.isValid()?a:null},getMinDate:function(){let a=A(this.$refs.minDate?.value);return a.isValid()?a:null},getSelectedDate:function(){if(this.state===void 0||this.state===null)return null;let a=A(this.state);return a.isValid()?a:null},togglePanelVisibility:function(){this.isOpen()||(this.focusedDate=this.getSelectedDate()??this.getMinDate()??A().tz(s),this.setupDaysGrid()),this.$refs.panel.toggle(this.$refs.button)},selectDate:function(a=null){a&&this.setFocusedDay(a),this.focusedDate??(this.focusedDate=A().tz(s)),this.setState(this.focusedDate),e&&this.togglePanelVisibility()},setDisplayText:function(){this.displayText=this.getSelectedDate()?this.getSelectedDate().format(n):""},setMonths:function(){this.months=A.months()},setDayLabels:function(){this.dayLabels=this.getDayLabels()},setupDaysGrid:function(){this.focusedDate??(this.focusedDate=A().tz(s)),this.emptyDaysInFocusedMonth=Array.from({length:this.focusedDate.date(8-t).day()},(a,d)=>d+1),this.daysInFocusedMonth=Array.from({length:this.focusedDate.daysInMonth()},(a,d)=>d+1)},setFocusedDay:function(a){this.focusedDate=(this.focusedDate??A().tz(s)).date(a)},setState:function(a){if(a===null){this.state=null,this.setDisplayText();return}this.dateIsDisabled(a)||(this.state=a.hour(this.hour??0).minute(this.minute??0).second(this.second??0).format("YYYY-MM-DD HH:mm:ss"),this.setDisplayText())},isOpen:function(){return this.$refs.panel?.style.display==="block"}}}var Zn={ar:fn(),bs:ln(),ca:mn(),cs:cn(),cy:hn(),da:Mn(),de:yn(),en:Yn(),es:pn(),fa:Dn(),fi:Ln(),fr:vn(),hi:gn(),hu:Sn(),hy:bn(),id:kn(),it:Hn(),ja:Tn(),ka:jn(),km:wn(),ku:Cn(),ms:On(),my:zn(),nl:An(),pl:In(),pt_BR:xn(),pt_PT:qn(),ro:Nn(),ru:En(),sv:Fn(),tr:Jn(),uk:Un(),vi:Wn(),zh_CN:Pn(),zh_TW:Rn()};export{li as default};
