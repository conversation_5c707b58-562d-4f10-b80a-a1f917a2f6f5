
import { useState, useEffect } from 'react';
import { Button } from './ui/buttons';
// import { Input } from '@/components/ui/input';
import { Badge } from './ui/badges';
import { Search, DollarSign, MapPin, Clock, Star, Heart, Users, Bookmark } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from './../config';

type ArtistProps = {
  professionals?: any[];
  query?:string;
  onDislike?: (id: number) => void;
};

const DesignersSection:React.FC<ArtistProps>  = ({professionals,query,onDislike})=> {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState('All');
  const [availableForWork, setAvailableForWork] = useState(false);
  const [likedProfiles, setLikedProfiles] = useState<number[]>([]);
  const [likesCount, setLikesCount] = useState<{ [key: number]: number }>(
      (professionals ?? []).reduce((acc, item) => ({ ...acc, [item.id]: item.likes }), {})
    );

  const filters = [
    'All', 'Product Design', 'Web Design', 'Illustration', 'Branding', 
    'Animation', 'Mobile', 'Typography', 'Print'
  ];

  const getUrlProlfil = (path : string)  => {
        return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
    };

  useEffect(() => {
      const token = localStorage.getItem("token");
      if (!token || professionals===undefined) return;
      // Pour chaque item, on vérifie le statut du like
      const fetchLikeStatuses = async () => {
        const likedIds: number[] = [];
        await Promise.all(professionals.map(async (item) => {
          try {
            const res = await fetch(
              `${API_BASE_URL}/api/professionals/${item.id}/like/status`,
              {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${token}`,
                },
              }
            );
            if (res.ok) {
              const data = await res.json();
              if (data.success && data.data.liked) {
                likedIds.push(item.id);
              }
            }
          } catch (e) {
            // ignore
          }
        }));
        setLikedProfiles(likedIds);
      };
      fetchLikeStatuses();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionals]);

//   const handleLike = async (professionalId: number) => {
//   const token = localStorage.getItem("token");

//   if (!token) {
//     navigate("/login");
//     return;
//   }

//   try {
//     const res = await fetch(`${API_BASE_URL}/api/professionals/${professionalId}/like`, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//         Authorization: `Bearer ${token}`,
//       },
//     });

//     if (!res.ok) throw new Error("Erreur lors du like");

//     const data = await res.json();

//     if (data.success) {
//       const { liked } = data.data;

//       setLikedProfiles(prev =>
//         liked
//           ? [...prev, professionalId]
//           : prev.filter(id => id !== professionalId)
//       );
//     }
//   } catch (err) {
//     console.error("Erreur lors du like :", err);
//   }
// };

const handleLike = async (e: React.MouseEvent, professionalProfileId: number) => {
    e.stopPropagation();

    const token = localStorage.getItem("token");

    if (!token) {
      navigate("/login");
      return;
    }

    try {
      let response;
      let wasDislike = false;
      if (likedProfiles.includes(professionalProfileId)) {
        // Déjà liké → dislike (DELETE)
        response = await fetch(
          `${API_BASE_URL}/api/professionals/${professionalProfileId}/like`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`,
            },
          }
        );
        wasDislike = true;
      } else {
        // Pas encore liké → like (POST)
        response = await fetch(
          `${API_BASE_URL}/api/professionals/${professionalProfileId}/like`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`,
            },
          }
        );
      }

      if (!response.ok) throw new Error("Erreur lors du like/dislike");

      const data = await response.json();

      if (data.success) {
        const { liked, total_likes } = data.data;

        setLikedProfiles(prev =>
          liked
            ? [...prev, professionalProfileId]
            : prev.filter(id => id !== professionalProfileId)
        );

        setLikesCount(prev => ({
          ...prev,
          [professionalProfileId]: total_likes
        }));

        // Si c'était un dislike et onDislike est fourni, on l'appelle
        if (wasDislike && onDislike) {
          onDislike(professionalProfileId);
        }
      }
    } catch (err) {
      console.error("Erreur like/dislike :", err);
    }
  };


  if (professionals?.length === 0) {
    return (
      <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-24 h-24 text-gray-400 mb-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={1.5}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
          />
        </svg>
        <h2 className="text-2xl font-semibold">No professionals found</h2>
        <p className="text-gray-500 mt-2 max-w-md">
          We couldn’t find any professionals matching your search or category. Try again with a different filter.
        </p>
      </div>
    );
  }

  return (
    <section className="bg-gradient-to-b from-gray-50 to-white pt-20 pb-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">All 3D Artist</h1>
          <p className="text-lg text-gray-600 mb-2">Showing results for "{query}"</p>
          <p className="text-sm text-gray-500 italic">*Thinking - writing page dashboard new design workflow</p>
        </div>

        {/* Designers List */}
        <div className="space-y-8">
          {professionals && professionals.map((professional) => (
            <div key={professional.id} className="bg-white border border-gray-100 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center gap-5">
                  {/* <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg">
                    {professional.first_name.charAt(0)}
                  </div> */}
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg overflow-hidden">
                    {professional.profile_picture_path ? (
                      <img
                        src={getUrlProlfil(professional.profile_picture_path)}
                        alt={professional.first_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      professional.first_name.charAt(0)
                    )}
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-2xl font-bold text-gray-900">{professional.first_name} {professional.last_name}</h3>
                      {/* {designer.isPro && ( */}
                      <Badge className="bg-gradient-to-r from-orange-400 to-orange-600 text-white px-3 py-1 text-xs font-semibold shadow-md">
                        PRO
                      </Badge>
                      {/* )} */}
                    </div>
                    <div className="flex items-center gap-6 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                        <span className="font-semibold">{professional.rating}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-5 h-5 text-blue-500" />
                        <span>{professional.city || 'City'}, {professional.country || 'Country'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5 text-orange-500" />
                        <span>{professional.availability_status}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5 text-purple-500" />
                        <span>{professional.service_offer.length} Service offer</span>
                      </div>
                      {/* <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>{designer.projects} projects completed</span>
                      </div> */}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {/* <Button variant="ghost" size="sm" className="h-10 w-10 rounded-full hover:bg-gray-100 transition-colors">
                    <Heart className="w-5 h-5 text-gray-400 hover:text-red-400 transition-colors" />
                  </Button> */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-10 w-10 rounded-full hover:bg-gray-100 transition-colors group"
                    // onClick={() => handleLike(professional.id)}
                    onClick={(e) => handleLike(e, professional.id)}
                  >
                    <Heart
                      className={`w-5 h-5 transition-colors duration-300 ${
                        likedProfiles.includes(professional.id)
                          ? 'text-red-500'
                          : 'text-gray-400 group-hover:text-pink-400'
                      }`}
                    />
                  </Button>

                  <Button variant="ghost" size="sm" className="h-10 w-10 rounded-full hover:bg-gray-100 transition-colors">
                    <Bookmark className="w-5 h-5 text-gray-400 hover:text-blue-400 transition-colors" />
                  </Button>
                  <Button className="bg-gradient-to-r from-gray-900 to-gray-700 text-white hover:from-gray-800 hover:to-gray-600 px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    onClick={() => navigate(`/professionals/${professional.id}`)}
                  >
                    Get in touch
                  </Button>
                </div>
              </div>

              {(professional.achievements?.length ?? 0) > 0 ? (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
                    {[...(professional.achievements ?? [])]
                      .slice(0, 5)
                      .map((project, i) => (
                        <img
                          key={i}
                          src={project.image_url}
                          alt={project.title}
                          title={project.title}
                          className="rounded-md object-cover w-full aspect-square cursor-pointer"
                          onClick={() =>
                            navigate('/details-search', {
                              state: { project },
                            })
                          }
                        />
                      ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 italic">
                    Aucun projet réalisé n’est disponible pour ce professionnel.
                  </div>
                )}

              {/* Skills Tags (professional.service_offer?.length ?? 0) > 0 ||  ...(professional.service_offer ?? []), */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {professional.skills.map((skill:string, index:any) => (
                    <Badge 
                      key={index} 
                      variant="secondary" 
                      className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-1 text-sm font-medium rounded-full transition-colors cursor-pointer"
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DesignersSection;
