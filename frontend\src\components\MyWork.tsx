import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { API_BASE_URL } from '../config';

type Project = {
  id: number;
  title: string;
  cover_photo: string;
  description: string;
};

const MyWork: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/api/dashboard/projects`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch projects');
        }

        const data = await response.json();
        // The API returns { projects: [...] }, so we need to access data.projects
        setProjects(data.projects || []);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `${API_BASE_URL.replace('/api', '')}/storage/${imagePath}`;
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
      {projects.map(item => (
        <Link to={`/dashboard/project/${item.id}`} key={item.id} className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col no-underline text-current hover:shadow-lg transition-shadow duration-300">
          <div
            style={{
              backgroundImage: `url(${getImageUrl(item.cover_photo)})`,
            }}
            className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
            aria-label={item.title}
            role="img"
          />
          <div
            className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <div className="flex items-center gap-2.5">
              <span className="font-semibold truncate max-w-[180px]">{item.title}</span>
            </div>
            <div className="flex items-center gap-4 text-[#888]">
              {/* likes and views are not in the API response */}
            </div>
          </div>
        </Link>
      ))}
      {/* Add work  card */}
      <div
        className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer"
        onClick={() => navigate('/dashboard/create-project')}
        tabIndex={0}
        role="button"
        aria-label="Add work"
        onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') navigate('/dashboard/create-project'); }}
      >
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add work</div>
      </div>
      
    </div>
  );
};

export default MyWork; 