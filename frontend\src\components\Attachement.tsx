import React from 'react';

const fileBlocks = [
  { name: 'Files Name', date: 'Date added', type: 'Type of files' },
  { name: 'Files Name', date: 'Date added', type: 'Type of files' },
  { name: 'Files Name', date: 'Date added', type: 'Type of files' },
  { name: 'Link Files', date: 'Date added', type: '' },
];

const Attachement = () => {
  return (
    <div style={{ width: 1200, marginLeft: 0, marginBottom:124 }}>
      {/* File blocks */}
      {fileBlocks.map((file, idx) => (
        <div key={idx} style={{ width: 1200, background: '#f8f5f2', borderRadius: 12, padding: '22px 32px', display: 'flex', alignItems: 'center', marginBottom: 18, position: 'relative' }}>
          <div style={{ display: 'flex', flex: 1, gap: 48, alignItems: 'center' }}>
            <span style={{ minWidth: 120, fontSize: 16, color: '#222', fontWeight: 500 }}>{file.name}</span>
            <span style={{ minWidth: 120, fontSize: 16, color: '#222', fontWeight: 400 }}>{file.date}</span>
            {file.type && <span style={{ minWidth: 120, fontSize: 16, color: '#222', fontWeight: 400 }}>{file.type}</span>}
          </div>
          <button style={{ background: '#fff', borderRadius: 20, padding: '7px 24px', fontWeight: 500, fontSize: 15, color: '#222', border: 'none', marginRight: 18, boxShadow: '0 1px 4px #e5e1dc' }}>Download</button>
          <button style={{ background: 'none', border: 'none', color: '#888', fontSize: 20, cursor: 'pointer', position: 'absolute', top: 12, right: 12, lineHeight: 1 }} aria-label="Remove">×</button>
        </div>
      ))}
      {/* Bottom box */}
      <div style={{ width: 1200, border: '1px dashed #bbb', borderRadius: 10, marginTop: 36, padding: 32, display: 'flex', alignItems: 'center', justifyContent: 'space-between', background: '#fff' }}>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <button style={{ background: '#111', color: '#fff', border: 'none', borderRadius: 20, padding: '8px 32px', fontSize: 15, fontWeight: 500, marginBottom: 8, cursor: 'pointer' }}>Add a download link</button>
          <span style={{ fontSize: 14, color: '#444' }}>Use <a href="#" style={{ color: '#888', textDecoration: 'underline' }}>Wetranfret</a> to create link</span>
        </div>
        <div style={{ minWidth: 260, display: 'flex', alignItems: 'center', background: '#f8f5f2', borderRadius: 8, padding: '12px 18px', border: '3px solid #f3f0ec' }}>
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: 12 }}><path d="M3 9.5V19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9.5"/><path d="M16 3H8a2 2 0 0 0-2 2v2.5h12V5a2 2 0 0 0-2-2z"/><polyline points="3 9.5 12 16 21 9.5"/></svg>
          <span style={{ fontWeight: 500, fontSize: 16, color: '#222', marginRight: 12 }}>Menu Item</span>
          <span style={{ background: '#e5e1dc', color: '#222', borderRadius: 12, fontSize: 13, fontWeight: 600, padding: '2px 10px', marginRight: 12 }}>99+</span>
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="6 9 12 15 18 9"/></svg>
        </div>
      </div>
    </div>
  );
};

export default Attachement; 