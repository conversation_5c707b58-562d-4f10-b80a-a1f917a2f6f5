# Guide du Back-Office Hi3D

## Introduction

Le back-office Hi3D est une interface d'administration complète construite avec Laravel Filament v3. Il permet aux administrateurs de gérer tous les aspects de la plateforme Hi3D.

## Accès au Back-Office

### URL d'accès
- **Local**: `http://localhost:8000/admin`
- **Production**: `https://votre-domaine.com/admin`

### Comptes par défaut
- **Email**: `<EMAIL>`
- **Mot de passe**: `admin123`
- **Rôle**: Super Administrateur

## Fonctionnalités

### 1. Tableau de Bord
- **Statistiques en temps réel** : Nombre d'utilisateurs, professionnels, clients, offres
- **Graphiques** : Évolution des inscriptions sur 30 jours
- **Widgets personnalisés** : Métriques clés de la plateforme

### 2. Gestion des Utilisateurs
- **CRUD complet** : <PERSON><PERSON><PERSON>, lire, modifier, supprimer des utilisateurs
- **Filtres avancés** : Par type de compte, statut du profil, rôle
- **Gestion des rôles** : user, moderator, admin, super_admin
- **Vérification email** : Statut de vérification des emails

### 3. Gestion des Profils
- **Profils Professionnels** : Gestion complète des profils pros
- **Profils Clients** : Administration des comptes clients
- **Validation** : Modération et validation des profils

### 4. Gestion des Offres
- **Offres Ouvertes** : Modération des offres publiées
- **Services** : Gestion des services proposés
- **Applications** : Suivi des candidatures aux offres

### 5. Communications
- **Messages** : Modération des échanges entre utilisateurs
- **Contacts** : Gestion des demandes de contact
- **Historique** : Suivi complet des communications

### 6. Outils d'Administration

#### Gestion Meilisearch
- **Réindexation** : Réindexer tous les modèles
- **Statut des index** : Vérification de l'état des index
- **Commandes** : Exécution des commandes Scout

#### Logs Système
- **Consultation** : Visualisation des logs Laravel
- **Téléchargement** : Export des fichiers de logs
- **Nettoyage** : Vidage des logs

#### Commandes Artisan
- **Cache** : Gestion du cache (clear, config, routes)
- **Optimisation** : Optimisation de l'application
- **Migrations** : Exécution des migrations

## Rôles et Permissions

### Super Administrateur
- Accès complet à toutes les fonctionnalités
- Gestion des autres administrateurs
- Maintenance système critique
- Accès aux outils de développement

### Administrateur
- Gestion des utilisateurs et contenus
- Modération complète
- Accès aux statistiques
- Outils de maintenance de base

### Modérateur
- Modération des contenus uniquement
- Accès limité aux statistiques
- Pas d'accès aux outils système

### Utilisateur
- Aucun accès au back-office
- Compte utilisateur standard

## Sécurité

### Authentification
- Connexion obligatoire avec email/mot de passe
- Vérification des rôles à chaque requête
- Protection CSRF activée

### Middleware de Protection
- `AdminAccess` : Vérifie les permissions admin
- `Authenticate` : Authentification obligatoire
- Protection contre les accès non autorisés

### Journalisation
- Toutes les actions admin sont loggées
- Traçabilité complète des modifications
- Audit trail des opérations sensibles

## Installation et Configuration

### Prérequis
- PHP 8.1+
- Laravel 10+
- Base de données configurée
- Meilisearch installé et configuré

### Installation
```bash
# Installation de Filament
composer require filament/filament:"^3.0"

# Installation du panel admin
php artisan filament:install --panels

# Création d'un utilisateur admin
php artisan admin:create-user
```

### Configuration
Le panel admin est configuré dans `app/Providers/Filament/AdminPanelProvider.php`

## Maintenance

### Tâches Régulières
1. **Vérification des logs** : Consulter les erreurs quotidiennement
2. **Réindexation Meilisearch** : Hebdomadaire ou après modifications importantes
3. **Nettoyage du cache** : Après les mises à jour
4. **Sauvegarde** : Avant toute opération critique

### Surveillance
- Surveiller les métriques du dashboard
- Vérifier les performances de recherche
- Contrôler la croissance des logs

## Dépannage

### Problèmes Courants

#### Accès refusé
- Vérifier le rôle de l'utilisateur
- S'assurer que l'email est vérifié
- Contrôler les permissions

#### Erreurs Meilisearch
- Vérifier que le service est démarré
- Réindexer les modèles
- Consulter les logs Meilisearch

#### Performance lente
- Vider le cache
- Optimiser l'application
- Vérifier les logs d'erreurs

### Support
Pour toute assistance technique, consulter :
- Les logs système dans le back-office
- La documentation Laravel Filament
- Les logs de l'application Laravel

## Évolutions Futures

### Fonctionnalités Prévues
- Authentification à deux facteurs
- Notifications en temps réel
- Rapports avancés
- API d'administration
- Gestion des sauvegardes automatiques

### Améliorations
- Interface mobile optimisée
- Thèmes personnalisables
- Widgets configurables
- Intégrations tierces
