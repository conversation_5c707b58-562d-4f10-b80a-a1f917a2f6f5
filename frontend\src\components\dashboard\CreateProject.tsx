import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Briefcase,
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Upload,
  X,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';

interface Category {
  id: number;
  name: string;
}

const CreateProject: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    coverPhoto: null as File | null,
    galleryPhotos: [] as File[],
    youtubeLink: '',
  });

  // Categories (would typically come from an API)
  const [categories] = useState<Category[]>([
    { id: 1, name: 'Modélisation 3D' },
    { id: 2, name: 'Animation 3D' },
    { id: 3, name: 'Rendu 3D' },
    { id: 4, name: 'Texturing' },
    { id: 5, name: 'Rigging' },
    { id: 6, name: 'Conception de personnages' },
    { id: 7, name: 'Environnements virtuels' },
    { id: 8, name: 'Effets spéciaux' },
    { id: 9, name: 'Réalité virtuelle' },
    { id: 10, name: 'Réalité augmentée' }
  ]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle cover photo upload
  const handleCoverPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({
        ...prev,
        coverPhoto: e.target.files![0]
      }));
    }
  };

  // Handle gallery photos upload
  const handleGalleryPhotosChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFormData(prev => ({
        ...prev,
        galleryPhotos: Array.from(e.target.files as FileList)
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.title.trim()) {
      setError('Le titre du projet est requis');
      return;
    }

    if (!formData.description.trim()) {
      setError('La description du projet est requise');
      return;
    }

    if (!formData.category) {
      setError('La catégorie du projet est requise');
      return;
    }

    if (!formData.coverPhoto) {
      setError('La photo de couverture est requise');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      // Create FormData object for file uploads
      const projectFormData = new FormData();
      projectFormData.append('title', formData.title);
      projectFormData.append('description', formData.description);
      projectFormData.append('category', formData.category);
      projectFormData.append('coverPhoto', formData.coverPhoto);
      formData.galleryPhotos.forEach((file, index) => {
        projectFormData.append(`galleryPhotos[${index}]`, file);
      });
      projectFormData.append('youtubeLink', formData.youtubeLink);

      // Send request to API
      const response = await fetch(`${API_BASE_URL}/api/dashboard/projects`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: projectFormData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la création du projet');
      }

      const data = await response.json();

      setSuccess('Projet créé avec succès!');

      // Redirect to edit portfolio page after a short delay
      setTimeout(() => {
        navigate('/edit-portfolio');
      }, 1500);

    } catch (err) {
      console.error('Error creating project:', err);
      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de la création du projet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout
      title="Créer un nouveau projet"
      subtitle="Décrivez votre projet pour trouver les meilleurs professionnels"
      actions={
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
        >
          Annuler
        </Button>
      }
    >
      {/* Alert messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-start">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-3 mt-0.5" />
          <div>
            <p className="text-red-800 font-medium">Erreur</p>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-start">
          <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
          <div>
            <p className="text-green-800 font-medium">Succès</p>
            <p className="text-green-700 text-sm">{success}</p>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden mb-6">
        <div className="p-6">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Project title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-neutral-700 mb-1">
                  Titre du projet *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Placeholder"
                  required
                />
              </div>

              {/* Project cover photo */}
              <div>
                <label htmlFor="coverPhoto" className="block text-sm font-medium text-neutral-700 mb-1">
                  Photo de couverture *
                </label>
                <input
                  type="file"
                  id="coverPhoto"
                  name="coverPhoto"
                  accept="image/*"
                  onChange={handleCoverPhotoChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
                {formData.coverPhoto && (
                  <div className="mt-2 text-xs text-neutral-500">{formData.coverPhoto.name}</div>
                )}
              </div>

              {/* Project gallery photos */}
              <div>
                <label htmlFor="galleryPhotos" className="block text-sm font-medium text-neutral-700 mb-1">
                  Photo galerie
                </label>
                <input
                  type="file"
                  id="galleryPhotos"
                  name="galleryPhotos"
                  accept="image/*"
                  multiple
                  onChange={handleGalleryPhotosChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
                {formData.galleryPhotos.length > 0 && (
                  <div className="mt-2 text-xs text-neutral-500">
                    {formData.galleryPhotos.map((file, idx) => (
                      <div key={idx}>{file.name}</div>
                    ))}
                  </div>
                )}
              </div>

              {/* Project category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-1">
                  Catégorie *
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="">Selection parmi les catégories de services à définir</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Project description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Texte avec possibilité de créer une simple mise en page, exemple caractère gras, liste à point,…"
                  required
                />
              </div>

              {/* Youtube video link */}
              <div>
                <label htmlFor="youtubeLink" className="block text-sm font-medium text-neutral-700 mb-1">
                  Video youtube link
                </label>
                <input
                  type="text"
                  id="youtubeLink"
                  name="youtubeLink"
                  value={formData.youtubeLink}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Placeholder"
                />
              </div>

              {/* Submit button */}
              <div className="flex justify-end mt-6">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="w-full md:w-auto py-3 px-6 text-lg font-semibold"
                  style={{ backgroundColor: '#2980b9', color: 'white', borderRadius: '0.5rem' }}
                >
                  {loading ? 'Création en cours...' : 'Créer le projet'}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Tips section */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
          <div>
            <p className="text-blue-800 font-medium">Conseils pour un projet réussi</p>
            <ul className="text-blue-700 text-sm mt-2 space-y-1 list-disc list-inside">
              <li>Soyez précis dans votre description pour attirer les bons professionnels</li>
              <li>Définissez un budget réaliste pour la qualité que vous recherchez</li>
              <li>Ajoutez des références visuelles ou des exemples pour clarifier vos attentes</li>
              <li>Précisez les délais et les étapes importantes du projet</li>
            </ul>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CreateProject;
