import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { API_BASE_URL } from "../../config";
import Header from "../Header";
import Footer from "../Footer";

// Define the types for your project data
interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  cover_photo: string;
  gallery_photos: { name: string; path: string; size: number; type: string }[];
  youtube_link: string;
  status: string;
  created_at: string;
  updated_at: string;
  user_id: number;
}

const ProjectDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProject = async () => {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("Authentication required. Please log in.");
        setLoading(false);
        navigate("/login");
        return;
      }

      try {
        const response = await fetch(
          `${API_BASE_URL}/api/dashboard/projects/${id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch project details.");
        }

        const data = await response.json();
        setProject(data.project);
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [id, navigate]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen text-red-500">
        {error}
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex justify-center items-center h-screen">
        Project not found.
      </div>
    );
  }

  return (
    <div style={{ background: "#fff", minHeight: "100vh" }}>
      <Header />
      <div className="container mx-auto p-4 md:p-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src={`${API_BASE_URL}/storage/${project.cover_photo}`}
            alt={project.title}
            className="w-full h-64 object-cover"
          />
          <div className="p-6">
            <h1 className="text-3xl font-bold mb-2">{project.title}</h1>
            <p className="text-gray-600 mb-4">Category: {project.category}</p>
            <p className="text-gray-700 mb-4">{project.description}</p>

            {project.gallery_photos && project.gallery_photos.length > 0 && (
              <div className="mb-4">
                <h2 className="text-2xl font-semibold mb-2">Gallery</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {project.gallery_photos.map((photo, index) => (
                    <img
                      key={index}
                      src={`${API_BASE_URL}/storage/${photo.path}`}
                      alt={photo.name}
                      className="w-full h-auto rounded-lg"
                    />
                  ))}
                </div>
              </div>
            )}

            {project.youtube_link && (
              <div className="mb-4">
                <h2 className="text-2xl font-semibold mb-2">Video</h2>
                <div className="aspect-w-16 aspect-h-9">
                  <iframe
                    src={project.youtube_link.replace("watch?v=", "embed/")}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="w-full h-full"
                  ></iframe>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center mt-6">
              <span
                className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  project.status === "open"
                    ? "bg-green-200 text-green-800"
                    : "bg-red-200 text-red-800"
                }`}
              >
                {project.status}
              </span>
              <button
                onClick={() => navigate(-1)}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProjectDetails;
