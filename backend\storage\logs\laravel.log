[2025-07-08 13:48:53] local.ERROR: SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. (Connection: pgsql, SQL: drop table if exists "dashboard_projects") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2BP01): SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. (Connection: pgsql, SQL: drop table if exists \"dashboard_projects\") at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('dashboard_proje...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2024_01_01_000014_create_dashboard_projects_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 {main}

[previous exception] [object] (PDOException(code: 2BP01): SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('drop table if e...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('dashboard_proje...')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2024_01_01_000014_create_dashboard_projects_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 {main}
"} 
[2025-07-08 14:16:15] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\Authenticate.php(15): route('login')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-07-08 14:17:14] testing.INFO: setSkillsAttribute appelé avec: ["Python","Laravel","Git","CI\/CD","PHP","AWS","Django","React"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Skills encodé directement: ["Python","Laravel","Git","CI\/CD","PHP","AWS","Django","React"]  
[2025-07-08 14:17:14] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Languages encodé directement: ["Anglais","Espagnol","Fran\u00e7ais"]  
[2025-07-08 14:17:14] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","D\u00e9veloppement Mobile","Formation"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","D\u00e9veloppement Mobile","Formation"]  
[2025-07-08 14:17:15] testing.INFO: setSkillsAttribute appelé avec: ["Agile","JavaScript","Vue.js","React","Python","Java","C#","PostgreSQL"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Skills encodé directement: ["Agile","JavaScript","Vue.js","React","Python","Java","C#","PostgreSQL"]  
[2025-07-08 14:17:15] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol","Anglais"]  
[2025-07-08 14:17:15] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Services_offered encodé directement: ["Formation","Maintenance"]  
[2025-07-08 14:17:16] testing.INFO: setSkillsAttribute appelé avec: ["MySQL","Python","Kubernetes","AWS","PostgreSQL","Agile","Git"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Skills encodé directement: ["MySQL","Python","Kubernetes","AWS","PostgreSQL","Agile","Git"]  
[2025-07-08 14:17:16] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-08 14:17:16] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Maintenance"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Services_offered encodé directement: ["Consulting","Maintenance"]  
[2025-07-08 14:17:17] testing.INFO: setSkillsAttribute appelé avec: ["Django","React","Laravel","PHP",".NET","PostgreSQL","Docker"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Skills encodé directement: ["Django","React","Laravel","PHP",".NET","PostgreSQL","Docker"]  
[2025-07-08 14:17:17] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Languages encodé directement: ["Allemand","Espagnol"]  
[2025-07-08 14:17:17] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 14:17:17] testing.INFO: setSkillsAttribute appelé avec: ["CI\/CD","Kubernetes","MySQL"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Skills encodé directement: ["CI\/CD","Kubernetes","MySQL"]  
[2025-07-08 14:17:17] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 14:17:17] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web"]  
[2025-07-08 14:18:19] testing.INFO: setSkillsAttribute appelé avec: ["AWS","Agile","PostgreSQL","MySQL"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Skills encodé directement: ["AWS","Agile","PostgreSQL","MySQL"]  
[2025-07-08 14:18:19] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Languages encodé directement: ["Espagnol","Allemand","Fran\u00e7ais"]  
[2025-07-08 14:18:19] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 14:18:19] testing.INFO: setSkillsAttribute appelé avec: ["CI\/CD","JavaScript","Java","Kubernetes"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Skills encodé directement: ["CI\/CD","JavaScript","Java","Kubernetes"]  
[2025-07-08 14:18:19] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Anglais"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Languages encodé directement: ["Allemand","Anglais"]  
[2025-07-08 14:18:19] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["React","Kubernetes","Git","Python","AWS","Spring","Node.js","MongoDB"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["React","Kubernetes","Git","Python","AWS","Spring","Node.js","MongoDB"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["MongoDB",".NET","AWS","Django","Git","Spring"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["MongoDB",".NET","AWS","Django","Git","Spring"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Espagnol","Anglais","Fran\u00e7ais"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Maintenance"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Mobile","Maintenance"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["Django","Scrum","Laravel","PHP"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["Django","Scrum","Laravel","PHP"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Anglais"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Allemand","Espagnol","Anglais"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 18:55:07] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","Vue.js","MySQL"] (type: array)  
[2025-07-08 18:55:07] local.INFO: Skills encodé directement: ["PHP","Laravel","React","Vue.js","MySQL"]  
[2025-07-08 18:55:07] local.INFO: setLanguagesAttribute appelé avec: ["French","English"] (type: array)  
[2025-07-08 18:55:07] local.INFO: Languages encodé directement: ["French","English"]  
[2025-07-08 18:55:07] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 18:55:07] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 18:55:07] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:55:07] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel"] (type: array)  
[2025-07-08 18:56:09] local.INFO: Skills encodé directement: ["PHP","Laravel"]  
[2025-07-08 18:56:09] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:09:52] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel"] (type: array)  
[2025-07-08 19:09:52] local.INFO: Skills encodé directement: ["PHP","Laravel"]  
[2025-07-08 19:09:52] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:09:52] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:09:52] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:09:52] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:12:02] local.ERROR: Call to undefined method Illuminate\Cache\FileStore::getRedis() {"exception":"[object] (Error(code: 0): Call to undefined method Illuminate\\Cache\\FileStore::getRedis() at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php:692)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->__call('getRedis', Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('getRedis', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Services\\SearchCacheService.php(162): Illuminate\\Support\\Facades\\Facade::__callStatic('getRedis', Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Controllers\\Api\\SearchController.php(240): App\\Services\\SearchCacheService->getPopularSearches(5)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\SearchController->stats()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('stats', Array)
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\SearchController), 'stats')
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\SearchRateLimit.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SearchRateLimit->handle(Object(Illuminate\\Http\\Request), Object(Closure), '100', '1')
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\PerformanceMonitor.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\PerformanceMonitor->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\ValidateJsonPayload.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ValidateJsonPayload->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-08 19:16:01] local.INFO: setSkillsAttribute appelé avec: ["Meilisearch","Laravel Scout","Search","PHP","Elasticsearch"] (type: array)  
[2025-07-08 19:16:01] local.INFO: Skills encodé directement: ["Meilisearch","Laravel Scout","Search","PHP","Elasticsearch"]  
[2025-07-08 19:16:01] local.INFO: setLanguagesAttribute appelé avec: ["French","English"] (type: array)  
[2025-07-08 19:16:01] local.INFO: Languages encodé directement: ["French","English"]  
[2025-07-08 19:16:01] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:16:01] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:16:01] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:16:01] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:17:16] local.INFO: setSkillsAttribute appelé avec: ["Search","Laravel","Meilisearch","PHP","API"] (type: array)  
[2025-07-08 19:17:16] local.INFO: Skills encodé directement: ["Search","Laravel","Meilisearch","PHP","API"]  
[2025-07-08 19:17:16] local.INFO: setLanguagesAttribute appelé avec: ["French","English"] (type: array)  
[2025-07-08 19:17:16] local.INFO: Languages encodé directement: ["French","English"]  
[2025-07-08 19:17:16] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:17:16] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:17:16] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:17:16] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:24:30] local.ERROR: An option named "verbose" already exists. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\LogicException(code: 0): An option named \"verbose\" already exists. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\InputDefinition.php:234)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\InputDefinition.php(222): Symfony\\Component\\Console\\Input\\InputDefinition->addOption(Object(Symfony\\Component\\Console\\Input\\InputOption))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(403): Symfony\\Component\\Console\\Input\\InputDefinition->addOptions(Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Descriptor\\TextDescriptor.php(127): Symfony\\Component\\Console\\Command\\Command->mergeApplicationDefinition(false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Descriptor\\Descriptor.php(39): Symfony\\Component\\Console\\Descriptor\\TextDescriptor->describeCommand(Object(App\\Console\\Commands\\IndexSearchableModels), Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Helper\\DescriptorHelper.php(69): Symfony\\Component\\Console\\Descriptor\\Descriptor->describe(Object(Symfony\\Component\\Console\\Output\\ConsoleOutput), Object(App\\Console\\Commands\\IndexSearchableModels), Array)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\HelpCommand.php(73): Symfony\\Component\\Console\\Helper\\DescriptorHelper->describe(Object(Symfony\\Component\\Console\\Output\\ConsoleOutput), Object(App\\Console\\Commands\\IndexSearchableModels), Array)
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Symfony\\Component\\Console\\Command\\HelpCommand->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Symfony\\Component\\Console\\Command\\HelpCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-08 19:46:30] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:46:30] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:46:30] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:46:30] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 19:55:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 23:21:04] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:04] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:04] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Anglais"] (type: array)  
[2025-07-08 23:21:04] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand","Anglais"]  
[2025-07-08 23:21:04] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:21:04] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 23:21:05] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:05] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:05] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-08 23:21:05] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol"]  
[2025-07-08 23:21:05] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","Consulting","Formation"] (type: array)  
[2025-07-08 23:21:05] testing.INFO: Services_offered encodé directement: ["Maintenance","Consulting","Formation"]  
[2025-07-08 23:21:06] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:06] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 23:21:06] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Formation"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Formation"]  
[2025-07-08 23:21:06] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:06] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol","Anglais"]  
[2025-07-08 23:21:06] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Formation","Maintenance"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Services_offered encodé directement: ["Consulting","Formation","Maintenance"]  
[2025-07-08 23:21:06] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:06] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-08 23:21:06] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Services_offered encodé directement: ["Formation","Maintenance"]  
[2025-07-08 23:21:06] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:06] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Languages encodé directement: ["Allemand","Espagnol","Fran\u00e7ais"]  
[2025-07-08 23:21:06] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-08 23:21:06] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:06] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-08 23:21:06] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-08 23:21:06] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-08 23:21:07] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:21:07] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:21:07] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-08 23:21:07] testing.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais","Espagnol"]  
[2025-07-08 23:21:07] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation"] (type: array)  
[2025-07-08 23:21:07] testing.INFO: Services_offered encodé directement: ["Formation"]  
[2025-07-08 23:27:20] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:20] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Languages encodé directement: ["Fran\u00e7ais"]  
[2025-07-08 23:27:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting","Maintenance"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting","Maintenance"]  
[2025-07-08 23:27:20] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:20] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 23:27:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile"] (type: array)  
[2025-07-08 23:27:20] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Mobile"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation","Maintenance"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Formation","Maintenance"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["Consulting","Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Espagnol","Anglais"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Consulting"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["Formation","Consulting"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Allemand","Espagnol","Fran\u00e7ais"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"]  
[2025-07-08 23:27:21] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:27:21] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais"]  
[2025-07-08 23:27:21] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-08 23:27:21] testing.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-08 23:28:56] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:56] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais"]  
[2025-07-08 23:28:56] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 23:28:56] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:56] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 23:28:56] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Web","Formation"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Web","Formation"]  
[2025-07-08 23:28:56] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:56] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-08 23:28:56] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"]  
[2025-07-08 23:28:56] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:56] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Languages encodé directement: ["Espagnol","Anglais"]  
[2025-07-08 23:28:56] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance"]  
[2025-07-08 23:28:56] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:56] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais"]  
[2025-07-08 23:28:56] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","Consulting"] (type: array)  
[2025-07-08 23:28:56] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance","Consulting"]  
[2025-07-08 23:28:57] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:57] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Languages encodé directement: ["Espagnol","Allemand"]  
[2025-07-08 23:28:57] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Web","Consulting"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Web","Consulting"]  
[2025-07-08 23:28:57] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:57] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-08 23:28:57] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-08 23:28:57] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:28:57] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-08 23:28:57] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting","Maintenance"] (type: array)  
[2025-07-08 23:28:57] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting","Maintenance"]  
[2025-07-08 23:29:49] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:49] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais","Espagnol"]  
[2025-07-08 23:29:49] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Maintenance"]  
[2025-07-08 23:29:49] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:49] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais"]  
[2025-07-08 23:29:49] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Services_offered encodé directement: ["Formation","Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 23:29:49] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:49] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-08 23:29:49] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","Consulting"] (type: array)  
[2025-07-08 23:29:49] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance","Consulting"]  
[2025-07-08 23:29:50] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:50] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Languages encodé directement: ["Espagnol","Anglais"]  
[2025-07-08 23:29:50] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Services_offered encodé directement: ["Formation","Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 23:29:50] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:50] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Anglais"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais","Anglais"]  
[2025-07-08 23:29:50] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-08 23:29:50] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:50] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Languages encodé directement: ["Espagnol","Allemand","Fran\u00e7ais"]  
[2025-07-08 23:29:50] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-08 23:29:50] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:50] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Languages encodé directement: ["Allemand","Espagnol","Fran\u00e7ais"]  
[2025-07-08 23:29:50] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web","Consulting"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web","Consulting"]  
[2025-07-08 23:29:50] testing.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-08 23:29:50] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 23:29:50] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-08 23:29:50] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting"]  
[2025-07-09 01:04:32] local.ERROR: Filament\FilamentManager::getUserName(): Return value must be of type string, null returned {"view":{"view":"C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\resources\\views\\components\\avatar\\user.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1627103893 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1894</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1627103893\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","user":"<pre class=sf-dump id=sf-dump-767603845 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#3176</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Admin</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Hi3D</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:15:30</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$fYAVClwB2tVP.zTMBYuhqO2AN4EoZvTAjKLE2B4kWhG0JgOL3qmpO</span>\"
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_professional</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>profile_completed</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:15:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:43:40</span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"11 characters\">super_admin</span>\"
    \"<span class=sf-dump-key>google2fa_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>google2fa_enabled</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>google2fa_enabled_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Admin</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Hi3D</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:15:30</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$fYAVClwB2tVP.zTMBYuhqO2AN4EoZvTAjKLE2B4kWhG0JgOL3qmpO</span>\"
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_professional</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>profile_completed</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:15:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 00:43:40</span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"11 characters\">super_admin</span>\"
    \"<span class=sf-dump-key>google2fa_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>google2fa_enabled</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>google2fa_enabled_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"
    \"<span class=sf-dump-key>is_professional</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>profile_completed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>google2fa_enabled</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>google2fa_enabled_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">stripe_customer_id</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">is_professional</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"17 characters\">profile_completed</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">google2fa_secret</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">google2fa_enabled</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"20 characters\">google2fa_enabled_at</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-767603845\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","attributes":"<pre class=sf-dump id=sf-dump-821612761 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentAttributeBag</span> {<a class=sf-dump-ref>#3092</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#3176</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">pgsql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:16</span> [ &#8230;16]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:16</span> [ &#8230;16]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
    </samp>}
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-821612761\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slot":"<pre class=sf-dump id=sf-dump-1929290777 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\View\\ComponentSlot</span> {<a class=sf-dump-ref>#2792</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#2793</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"
</samp>}
</pre><script>Sfdump(\"sf-dump-1929290777\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__laravel_slots":"<pre class=sf-dump id=sf-dump-1592028005 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>__default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentSlot
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentSlot</span></span> {<a class=sf-dump-ref>#2792</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#2793</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">contents</span>: \"\"
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-1592028005\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":9,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Filament\\FilamentManager::getUserName(): Return value must be of type string, null returned at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php:450)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php(207): Filament\\FilamentManager->getUserName(Object(App\\Models\\User))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Filament\\FilamentManager->getNameForDefaultAvatar(Object(App\\Models\\User))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\AvatarProviders\\UiAvatarsProvider.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic('getNameForDefau...', Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php(417): Filament\\AvatarProviders\\UiAvatarsProvider->get(Object(App\\Models\\User))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\resources\\views\\components\\avatar\\user.blade.php(5): Filament\\FilamentManager->getUserAvatarUrl(Object(App\\Models\\User))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\resources\\views\\components\\user-menu.blade.php(30): Illuminate\\View\\Factory->renderComponent()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\resources\\views\\components\\topbar\\index.blade.php(133): Illuminate\\View\\Factory->renderComponent()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\resources\\views\\components\\layout\\index.blade.php(39): Illuminate\\View\\Factory->renderComponent()
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\4943bc92ebba41e8b0e508149542e0ad.blade.php(16): Illuminate\\View\\Factory->renderComponent()
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(335): Illuminate\\View\\View->render()
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\View\\Compilers\\BladeCompiler::render('    <?php $layo...', Array)
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(153): Illuminate\\Support\\Facades\\Facade::__callStatic('render', Array)
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(24): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::renderContentsIntoLayout('<div wire:snaps...', Object(Livewire\\Features\\SupportPageComponents\\PageComponentConfig))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Filament\\Pages\\Dashboard), '__invoke')
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\AdminAccess.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#92 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#95 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#96 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#97 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#98 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#117 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#118 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#119 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#120 {main}

[previous exception] [object] (TypeError(code: 0): Filament\\FilamentManager::getUserName(): Return value must be of type string, null returned at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php:450)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php(207): Filament\\FilamentManager->getUserName(Object(App\\Models\\User))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Filament\\FilamentManager->getNameForDefaultAvatar(Object(App\\Models\\User))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\AvatarProviders\\UiAvatarsProvider.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic('getNameForDefau...', Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\FilamentManager.php(417): Filament\\AvatarProviders\\UiAvatarsProvider->get(Object(App\\Models\\User))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\c5bb9ecac7b8b531dce17c451796dfdd.php(23): Filament\\FilamentManager->getUserAvatarUrl(Object(App\\Models\\User))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\20844b88a90b62a89124786462628c8a.php(49): Illuminate\\View\\Factory->renderComponent()
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\b3286bd7bad27af24360dbbfc0432cc3.php(314): Illuminate\\View\\Factory->renderComponent()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\c6e2ad814b0d03216dcca16b7dd30542.php(77): Illuminate\\View\\Factory->renderComponent()
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php(103): Illuminate\\View\\View->render()
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\33004a346074b9cf72f71c0d2e564792.php(17): Illuminate\\View\\Factory->renderComponent()
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(335): Illuminate\\View\\View->render()
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\View\\Compilers\\BladeCompiler::render('    <?php $layo...', Array)
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(153): Illuminate\\Support\\Facades\\Facade::__callStatic('render', Array)
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(24): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::renderContentsIntoLayout('<div wire:snaps...', Object(Livewire\\Features\\SupportPageComponents\\PageComponentConfig))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Filament\\Pages\\Dashboard), '__invoke')
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\AdminAccess.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#92 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#95 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#96 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#97 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#98 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#99 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#117 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#118 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#119 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#120 {main}
"} 
[2025-07-09 01:06:48] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = App\\\\Mo...', false)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = App\\\\Models\\\\U...', true)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode(' = App\\\\Models\\\\U...', true)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = App\\\\Models\\\\U...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-09 01:13:10] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":9,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\ef4a6a3830cff67da5b2878885c54300.php:1)
[stacktrace]
#0 {main}
"} 
[2025-07-09 01:14:31] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERREUR:  la colonne « status » n'existe pas
LINE 1: ...elect count(*) as aggregate from "contacts" where "status" =...
                                                             ^ (Connection: pgsql, SQL: select count(*) as aggregate from "contacts" where "status" = pending) {"view":{"view":"C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\resources\\views\\stats-overview-widget.blade.php","data":[]},"userId":9,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[42703]: Undefined column: 7 ERREUR:  la colonne « status » n'existe pas
LINE 1: ...elect count(*) as aggregate from \"contacts\" where \"status\" =...
                                                             ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"contacts\" where \"status\" = pending) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Widgets\\CommunicationsStats.php(28): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(43): App\\Filament\\Widgets\\CommunicationsStats->getStats()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(25): Filament\\Widgets\\StatsOverviewWidget->getCachedStats()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\resources\\views\\stats-overview-widget.blade.php(1): Filament\\Widgets\\StatsOverviewWidget->getColumns()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\\\...')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Widgets\\CommunicationsStats->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Widgets\\CommunicationsStats), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Widgets\\CommunicationsStats))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#70 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERREUR:  la colonne « status » n'existe pas
LINE 1: ...elect count(*) as aggregate from \"contacts\" where \"status\" =...
                                                             ^ (Connection: pgsql, SQL: select count(*) as aggregate from \"contacts\" where \"status\" = pending) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Widgets\\CommunicationsStats.php(28): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(43): App\\Filament\\Widgets\\CommunicationsStats->getStats()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(25): Filament\\Widgets\\StatsOverviewWidget->getCachedStats()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\076df32e48bdcfc1d250cfa5ff3ddf09.php(2): Filament\\Widgets\\StatsOverviewWidget->getColumns()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\\\...')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Widgets\\CommunicationsStats->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Widgets\\CommunicationsStats), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Widgets\\CommunicationsStats))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#70 {main}

[previous exception] [object] (PDOException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERREUR:  la colonne « status » n'existe pas
LINE 1: ...elect count(*) as aggregate from \"contacts\" where \"status\" =...
                                                             ^ at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Widgets\\CommunicationsStats.php(28): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(43): App\\Filament\\Widgets\\CommunicationsStats->getStats()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php(25): Filament\\Widgets\\StatsOverviewWidget->getCachedStats()
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\storage\\framework\\views\\076df32e48bdcfc1d250cfa5ff3ddf09.php(2): Filament\\Widgets\\StatsOverviewWidget->getColumns()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(37): include('C:\\\\Users\\\\<USER>\\\\...')
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(38): App\\Filament\\Widgets\\CommunicationsStats->Livewire\\Mechanisms\\ExtendBlade\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->Livewire\\Mechanisms\\HandleComponents\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Widgets\\CommunicationsStats), Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(104): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Widgets\\CommunicationsStats))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#72 {main}
"} 
[2025-07-09 01:27:47] local.ERROR: View [filament.pages.advanced-stats] not found. {"userId":9,"exception":"[object] (InvalidArgumentException(code: 0): View [filament.pages.advanced-stats] not found. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('filament.pages....', Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(137): Illuminate\\View\\FileViewFinder->find('filament.pages....')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('filament.pages....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Pages\\BasePage.php(44): view('filament.pages....', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Pages\\BasePage->render()
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(271): Livewire\\Wrapped->__call('render', Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(231): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->getView(Object(App\\Filament\\Pages\\AdvancedStats))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Pages\\AdvancedStats), '<div></div>')
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\AdvancedStats), '__invoke')
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#72 {main}
"} 
[2025-07-10 18:41:08] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:41:08] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:41:19] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:41:19] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:41:19] local.INFO: setLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: array)  
[2025-07-10 18:41:19] local.INFO: Languages encodé directement: ["French","English","Spanish"]  
[2025-07-10 18:41:19] local.INFO: setServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: array)  
[2025-07-10 18:41:19] local.INFO: Services_offered encodé directement: ["Web Development","Mobile Development"]  
[2025-07-10 18:41:19] local.INFO: getLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: string)  
[2025-07-10 18:41:19] local.INFO: getLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: string)  
[2025-07-10 18:41:19] local.INFO: getServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: string)  
[2025-07-10 18:41:19] local.INFO: getServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: string)  
[2025-07-10 18:41:22] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","Mobile"] (type: array)  
[2025-07-10 18:41:22] local.INFO: Skills encodé directement: ["PHP","Laravel","React","Mobile"]  
[2025-07-10 18:41:22] local.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-10 18:41:22] local.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-10 18:41:22] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-10 18:41:22] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting"]  
[2025-07-10 18:41:22] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:22] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:22] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:22] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:27] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:41:28] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:41:29] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:41:30] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:41:30] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (1, 1, 10, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (1, 10) returning "id")  
[2025-07-10 18:41:30] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:30] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:31] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:31] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:31] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:31] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:32] local.INFO: Services récupérés pour le professionnel ID 10: 1  
[2025-07-10 18:41:32] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:32] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:32] local.INFO: AchievementRequest rules method - request data: {"title":"Villa Moderne - Projet Résidentiel","organization":"Cabinet Architecture Moderne","date_obtained":"2024-06-15","description":"Conception et modélisation 3D complète d'une villa moderne de 250m² avec jardin paysager. Projet incluant plans architecturaux, rendus photoréalistes et visite virtuelle.","achievement_url":"https://portfolio.example.com/villa-moderne"} 
[2025-07-10 18:41:33] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:33] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:41:35] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:41:35] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:41:35] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: array)  
[2025-07-10 18:41:35] local.INFO: Languages encodé directement: ["Fran\u00e7ais"]  
[2025-07-10 18:41:35] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: array)  
[2025-07-10 18:41:35] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Maintenance"]  
[2025-07-10 18:41:35] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: string)  
[2025-07-10 18:41:35] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: string)  
[2025-07-10 18:41:35] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: string)  
[2025-07-10 18:41:35] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: string)  
[2025-07-10 18:41:42] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:41:42] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:41:42] local.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-10 18:41:42] local.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-10 18:41:42] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:41:42] local.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web"]  
[2025-07-10 18:41:42] local.INFO: getLanguagesAttribute appelé avec: ["Allemand"] (type: string)  
[2025-07-10 18:41:42] local.INFO: getLanguagesAttribute appelé avec: ["Allemand"] (type: string)  
[2025-07-10 18:41:42] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:41:42] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:41:46] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:41:46] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:41:46] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand"] (type: array)  
[2025-07-10 18:41:46] local.INFO: Languages encodé directement: ["Espagnol","Allemand"]  
[2025-07-10 18:41:46] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web","D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:41:46] local.INFO: Services_offered encodé directement: ["Consulting","D\u00e9veloppement Web","D\u00e9veloppement Mobile"]  
[2025-07-10 18:41:46] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Allemand"] (type: string)  
[2025-07-10 18:41:46] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Allemand"] (type: string)  
[2025-07-10 18:41:46] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:41:46] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:41:51] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:41:51] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:41:51] local.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Anglais"] (type: array)  
[2025-07-10 18:41:51] local.INFO: Languages encodé directement: ["Allemand","Espagnol","Anglais"]  
[2025-07-10 18:41:51] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","Consulting","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:41:51] local.INFO: Services_offered encodé directement: ["Maintenance","Consulting","D\u00e9veloppement Web"]  
[2025-07-10 18:41:51] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Espagnol","Anglais"] (type: string)  
[2025-07-10 18:41:51] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Espagnol","Anglais"] (type: string)  
[2025-07-10 18:41:51] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance","Consulting","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:41:51] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance","Consulting","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:41:55] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:41:55] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:41:55] local.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-10 18:41:55] local.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-10 18:41:55] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:41:55] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-10 18:41:55] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:55] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:41:55] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:41:55] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:42:03] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:42:03] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:42:03] local.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-10 18:42:03] local.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-10 18:42:03] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: array)  
[2025-07-10 18:42:03] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Formation"]  
[2025-07-10 18:42:03] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:42:03] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:42:03] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: string)  
[2025-07-10 18:42:03] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: string)  
[2025-07-10 18:42:07] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:42:07] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:42:07] local.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Anglais"] (type: array)  
[2025-07-10 18:42:07] local.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais","Anglais"]  
[2025-07-10 18:42:07] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-10 18:42:07] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting"]  
[2025-07-10 18:42:07] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Anglais"] (type: string)  
[2025-07-10 18:42:07] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Anglais"] (type: string)  
[2025-07-10 18:42:07] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:42:07] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:42:10] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:42:10] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:42:10] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais","Allemand"] (type: array)  
[2025-07-10 18:42:10] local.INFO: Languages encodé directement: ["Espagnol","Anglais","Allemand"]  
[2025-07-10 18:42:10] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:42:10] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-10 18:42:10] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais","Allemand"] (type: string)  
[2025-07-10 18:42:10] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais","Allemand"] (type: string)  
[2025-07-10 18:42:10] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:42:10] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:42:15] local.INFO: setSkillsAttribute appelé avec: ["Kubernetes","Scrum","CI\/CD","MongoDB","Git","Laravel"] (type: array)  
[2025-07-10 18:42:15] local.INFO: Skills encodé directement: ["Kubernetes","Scrum","CI\/CD","MongoDB","Git","Laravel"]  
[2025-07-10 18:42:15] local.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-10 18:42:15] local.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-10 18:42:15] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Formation","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:42:15] local.INFO: Services_offered encodé directement: ["Consulting","Formation","D\u00e9veloppement Web"]  
[2025-07-10 18:42:20] local.INFO: setSkillsAttribute appelé avec: ["CI\/CD","React","Docker","Azure","Node.js"] (type: array)  
[2025-07-10 18:42:20] local.INFO: Skills encodé directement: ["CI\/CD","React","Docker","Azure","Node.js"]  
[2025-07-10 18:42:20] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Espagnol"] (type: array)  
[2025-07-10 18:42:20] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand","Espagnol"]  
[2025-07-10 18:42:20] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-10 18:42:20] local.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-10 18:42:23] local.INFO: setSkillsAttribute appelé avec: ["Redis","Django","Spring"] (type: array)  
[2025-07-10 18:42:23] local.INFO: Skills encodé directement: ["Redis","Django","Spring"]  
[2025-07-10 18:42:23] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-10 18:42:23] local.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-10 18:42:23] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:42:23] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-10 18:42:24] local.INFO: setSkillsAttribute appelé avec: ["Laravel","Agile","Azure","MySQL","Spring","Git",".NET"] (type: array)  
[2025-07-10 18:42:24] local.INFO: Skills encodé directement: ["Laravel","Agile","Azure","MySQL","Spring","Git",".NET"]  
[2025-07-10 18:42:24] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: array)  
[2025-07-10 18:42:24] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Anglais","Allemand"]  
[2025-07-10 18:42:24] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:42:24] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-10 18:42:24] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: string)  
[2025-07-10 18:42:24] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: string)  
[2025-07-10 18:42:24] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:42:24] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:42:26] local.INFO: setSkillsAttribute appelé avec: ["Docker","Spring","React","Azure","PostgreSQL","Python","Redis",".NET"] (type: array)  
[2025-07-10 18:42:26] local.INFO: Skills encodé directement: ["Docker","Spring","React","Azure","PostgreSQL","Python","Redis",".NET"]  
[2025-07-10 18:42:26] local.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:42:26] local.INFO: Languages encodé directement: ["Allemand","Espagnol","Fran\u00e7ais"]  
[2025-07-10 18:42:26] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation"] (type: array)  
[2025-07-10 18:42:26] local.INFO: Services_offered encodé directement: ["Formation"]  
[2025-07-10 18:42:38] local.INFO: setSkillsAttribute appelé avec: ["Agile","JavaScript","Django","React","Git","Spring","MongoDB","Azure"] (type: array)  
[2025-07-10 18:42:38] local.INFO: Skills encodé directement: ["Agile","JavaScript","Django","React","Git","Spring","MongoDB","Azure"]  
[2025-07-10 18:42:38] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais"] (type: array)  
[2025-07-10 18:42:38] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Anglais"]  
[2025-07-10 18:42:38] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-10 18:42:38] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting"]  
[2025-07-10 18:42:40] local.INFO: setSkillsAttribute appelé avec: ["Node.js","Laravel","Java","Vue.js","C#"] (type: array)  
[2025-07-10 18:42:40] local.INFO: Skills encodé directement: ["Node.js","Laravel","Java","Vue.js","C#"]  
[2025-07-10 18:42:40] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:42:40] local.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais"]  
[2025-07-10 18:42:40] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:42:40] local.INFO: Services_offered encodé directement: ["Consulting","D\u00e9veloppement Web"]  
[2025-07-10 18:43:13] local.INFO: setSkillsAttribute appelé avec: ["PHP","JavaScript","Django","Laravel","C#","Vue.js"] (type: array)  
[2025-07-10 18:43:13] local.INFO: Skills encodé directement: ["PHP","JavaScript","Django","Laravel","C#","Vue.js"]  
[2025-07-10 18:43:14] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Espagnol"] (type: array)  
[2025-07-10 18:43:14] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand","Espagnol"]  
[2025-07-10 18:43:14] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-10 18:43:14] local.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-10 18:43:14] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Espagnol"] (type: string)  
[2025-07-10 18:43:14] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Espagnol"] (type: string)  
[2025-07-10 18:43:14] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting"] (type: string)  
[2025-07-10 18:43:14] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting"] (type: string)  
[2025-07-10 18:43:15] local.INFO: setSkillsAttribute appelé avec: ["Azure","JavaScript","Vue.js"] (type: array)  
[2025-07-10 18:43:15] local.INFO: Skills encodé directement: ["Azure","JavaScript","Vue.js"]  
[2025-07-10 18:43:15] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: array)  
[2025-07-10 18:43:15] local.INFO: Languages encodé directement: ["Fran\u00e7ais"]  
[2025-07-10 18:43:15] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Consulting","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:43:15] local.INFO: Services_offered encodé directement: ["Formation","Consulting","D\u00e9veloppement Web"]  
[2025-07-10 18:43:19] local.INFO: setSkillsAttribute appelé avec: ["Spring","JavaScript","Kubernetes","MySQL","CI\/CD","Docker","React"] (type: array)  
[2025-07-10 18:43:19] local.INFO: Skills encodé directement: ["Spring","JavaScript","Kubernetes","MySQL","CI\/CD","Docker","React"]  
[2025-07-10 18:43:19] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand","Anglais"] (type: array)  
[2025-07-10 18:43:19] local.INFO: Languages encodé directement: ["Espagnol","Allemand","Anglais"]  
[2025-07-10 18:43:19] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web","Maintenance"] (type: array)  
[2025-07-10 18:43:19] local.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web","Maintenance"]  
[2025-07-10 18:43:20] local.INFO: setSkillsAttribute appelé avec: ["JavaScript","MongoDB","Vue.js","Git","Scrum","React","Python"] (type: array)  
[2025-07-10 18:43:20] local.INFO: Skills encodé directement: ["JavaScript","MongoDB","Vue.js","Git","Scrum","React","Python"]  
[2025-07-10 18:43:20] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:43:20] local.INFO: Languages encodé directement: ["Anglais","Espagnol","Fran\u00e7ais"]  
[2025-07-10 18:43:20] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:43:20] local.INFO: Services_offered encodé directement: ["Consulting","D\u00e9veloppement Web"]  
[2025-07-10 18:43:20] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:20] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:20] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:43:20] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:43:23] local.INFO: setSkillsAttribute appelé avec: ["Scrum","PHP","MySQL","React"] (type: array)  
[2025-07-10 18:43:23] local.INFO: Skills encodé directement: ["Scrum","PHP","MySQL","React"]  
[2025-07-10 18:43:23] local.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-10 18:43:23] local.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais","Espagnol"]  
[2025-07-10 18:43:23] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:43:23] local.INFO: Services_offered encodé directement: ["Consulting","D\u00e9veloppement Mobile"]  
[2025-07-10 18:43:23] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Espagnol"] (type: string)  
[2025-07-10 18:43:23] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais","Espagnol"] (type: string)  
[2025-07-10 18:43:23] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:43:23] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:43:26] local.INFO: setSkillsAttribute appelé avec: ["Azure","Agile","Vue.js"] (type: array)  
[2025-07-10 18:43:26] local.INFO: Skills encodé directement: ["Azure","Agile","Vue.js"]  
[2025-07-10 18:43:26] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-10 18:43:26] local.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-10 18:43:26] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:43:26] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"]  
[2025-07-10 18:43:26] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:43:26] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:43:26] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:43:26] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:43:29] local.INFO: setSkillsAttribute appelé avec: ["Node.js","React","MongoDB","CI\/CD","Git",".NET","Django","C#"] (type: array)  
[2025-07-10 18:43:29] local.INFO: Skills encodé directement: ["Node.js","React","MongoDB","CI\/CD","Git",".NET","Django","C#"]  
[2025-07-10 18:43:29] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: array)  
[2025-07-10 18:43:29] local.INFO: Languages encodé directement: ["Fran\u00e7ais"]  
[2025-07-10 18:43:29] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:43:29] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"]  
[2025-07-10 18:43:32] local.INFO: setSkillsAttribute appelé avec: ["Agile",".NET","PostgreSQL"] (type: array)  
[2025-07-10 18:43:32] local.INFO: Skills encodé directement: ["Agile",".NET","PostgreSQL"]  
[2025-07-10 18:43:32] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Espagnol"] (type: array)  
[2025-07-10 18:43:32] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Anglais","Espagnol"]  
[2025-07-10 18:43:32] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:43:32] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:43:36] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:43:36] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:43:37] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:37] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:41] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:41] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:43] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:43] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:43] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:43] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:48] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:48] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:43:50] local.INFO: setSkillsAttribute appelé avec: ["C#","Scrum","Vue.js","MongoDB","Kubernetes","Node.js"] (type: array)  
[2025-07-10 18:43:50] local.INFO: Skills encodé directement: ["C#","Scrum","Vue.js","MongoDB","Kubernetes","Node.js"]  
[2025-07-10 18:43:50] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:43:50] local.INFO: Languages encodé directement: ["Anglais","Allemand","Fran\u00e7ais"]  
[2025-07-10 18:43:50] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:43:50] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-10 18:43:50] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:50] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:50] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:43:50] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:43:53] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:54] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:54] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:55] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:55] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (2, 2, 50, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (2, 50) returning "id")  
[2025-07-10 18:43:56] local.INFO: setSkillsAttribute appelé avec: ["Azure",".NET","Scrum"] (type: array)  
[2025-07-10 18:43:56] local.INFO: Skills encodé directement: ["Azure",".NET","Scrum"]  
[2025-07-10 18:43:56] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: array)  
[2025-07-10 18:43:56] local.INFO: Languages encodé directement: ["Fran\u00e7ais"]  
[2025-07-10 18:43:56] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance","Formation"] (type: array)  
[2025-07-10 18:43:56] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Maintenance","Formation"]  
[2025-07-10 18:43:56] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:56] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais"] (type: string)  
[2025-07-10 18:43:56] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance","Formation"] (type: string)  
[2025-07-10 18:43:56] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance","Formation"] (type: string)  
[2025-07-10 18:43:57] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:57] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:58] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:58] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:43:59] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (3, 3, 52, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (3, 52) returning "id")  
[2025-07-10 18:53:12] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:53:12] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:53:19] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:53:19] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:53:19] local.INFO: setLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: array)  
[2025-07-10 18:53:19] local.INFO: Languages encodé directement: ["French","English","Spanish"]  
[2025-07-10 18:53:19] local.INFO: setServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: array)  
[2025-07-10 18:53:19] local.INFO: Services_offered encodé directement: ["Web Development","Mobile Development"]  
[2025-07-10 18:53:19] local.INFO: getLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: string)  
[2025-07-10 18:53:19] local.INFO: getLanguagesAttribute appelé avec: ["French","English","Spanish"] (type: string)  
[2025-07-10 18:53:19] local.INFO: getServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: string)  
[2025-07-10 18:53:19] local.INFO: getServicesOfferedAttribute appelé avec: ["Web Development","Mobile Development"] (type: string)  
[2025-07-10 18:53:20] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","Mobile"] (type: array)  
[2025-07-10 18:53:20] local.INFO: Skills encodé directement: ["PHP","Laravel","React","Mobile"]  
[2025-07-10 18:53:20] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: array)  
[2025-07-10 18:53:20] local.INFO: Languages encodé directement: ["Anglais","Espagnol","Allemand"]  
[2025-07-10 18:53:20] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-10 18:53:20] local.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Mobile","Consulting"]  
[2025-07-10 18:53:20] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:20] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:20] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:20] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:22] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:53:22] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:53:23] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:53:24] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:53:24] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (1, 1, 10, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (1, 10) returning "id")  
[2025-07-10 18:53:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:24] local.INFO: Services récupérés pour le professionnel ID 10: 1  
[2025-07-10 18:53:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:24] local.INFO: AchievementRequest rules method - request data: {"title":"Villa Moderne - Projet Résidentiel","organization":"Cabinet Architecture Moderne","date_obtained":"2024-06-15","description":"Conception et modélisation 3D complète d'une villa moderne de 250m² avec jardin paysager. Projet incluant plans architecturaux, rendus photoréalistes et visite virtuelle.","achievement_url":"https://portfolio.example.com/villa-moderne"} 
[2025-07-10 18:53:25] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:25] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Consulting"] (type: string)  
[2025-07-10 18:53:26] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:26] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:26] local.INFO: setLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:53:26] local.INFO: Languages encodé directement: ["Allemand","Fran\u00e7ais"]  
[2025-07-10 18:53:26] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Maintenance"] (type: array)  
[2025-07-10 18:53:26] local.INFO: Services_offered encodé directement: ["Consulting","Maintenance"]  
[2025-07-10 18:53:26] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:26] local.INFO: getLanguagesAttribute appelé avec: ["Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:26] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","Maintenance"] (type: string)  
[2025-07-10 18:53:26] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting","Maintenance"] (type: string)  
[2025-07-10 18:53:28] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:28] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:28] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: array)  
[2025-07-10 18:53:28] local.INFO: Languages encodé directement: ["Anglais","Espagnol","Allemand"]  
[2025-07-10 18:53:28] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Formation"] (type: array)  
[2025-07-10 18:53:28] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Formation"]  
[2025-07-10 18:53:28] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:28] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol","Allemand"] (type: string)  
[2025-07-10 18:53:28] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Formation"] (type: string)  
[2025-07-10 18:53:28] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Formation"] (type: string)  
[2025-07-10 18:53:32] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:32] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:32] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:53:32] local.INFO: Languages encodé directement: ["Espagnol","Anglais","Fran\u00e7ais"]  
[2025-07-10 18:53:32] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:53:32] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:53:32] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:32] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:32] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:32] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:35] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:35] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:35] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:53:35] local.INFO: Languages encodé directement: ["Anglais","Allemand","Fran\u00e7ais"]  
[2025-07-10 18:53:35] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: array)  
[2025-07-10 18:53:35] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Formation"]  
[2025-07-10 18:53:35] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:35] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Fran\u00e7ais"] (type: string)  
[2025-07-10 18:53:35] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: string)  
[2025-07-10 18:53:35] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Formation"] (type: string)  
[2025-07-10 18:53:38] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:38] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:38] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: array)  
[2025-07-10 18:53:38] local.INFO: Languages encodé directement: ["Espagnol","Anglais"]  
[2025-07-10 18:53:38] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:53:38] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:53:38] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: string)  
[2025-07-10 18:53:38] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol","Anglais"] (type: string)  
[2025-07-10 18:53:38] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:38] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:40] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:40] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:40] local.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-10 18:53:40] local.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-10 18:53:40] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:53:40] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:53:40] local.INFO: getLanguagesAttribute appelé avec: ["Allemand"] (type: string)  
[2025-07-10 18:53:40] local.INFO: getLanguagesAttribute appelé avec: ["Allemand"] (type: string)  
[2025-07-10 18:53:40] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:40] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:44] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:44] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:44] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-10 18:53:44] local.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-10 18:53:44] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:53:44] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:53:44] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: string)  
[2025-07-10 18:53:44] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: string)  
[2025-07-10 18:53:44] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:44] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:46] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","JavaScript"] (type: array)  
[2025-07-10 18:53:46] local.INFO: Skills encodé directement: ["PHP","Laravel","React","JavaScript"]  
[2025-07-10 18:53:46] local.INFO: setLanguagesAttribute appelé avec: ["Anglais"] (type: array)  
[2025-07-10 18:53:46] local.INFO: Languages encodé directement: ["Anglais"]  
[2025-07-10 18:53:46] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance","Consulting"] (type: array)  
[2025-07-10 18:53:46] local.INFO: Services_offered encodé directement: ["Formation","Maintenance","Consulting"]  
[2025-07-10 18:53:46] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:53:46] local.INFO: getLanguagesAttribute appelé avec: ["Anglais"] (type: string)  
[2025-07-10 18:53:46] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","Maintenance","Consulting"] (type: string)  
[2025-07-10 18:53:46] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","Maintenance","Consulting"] (type: string)  
[2025-07-10 18:53:52] local.INFO: setSkillsAttribute appelé avec: ["MySQL","JavaScript","Docker","CI\/CD","PHP","PostgreSQL","Spring"] (type: array)  
[2025-07-10 18:53:52] local.INFO: Skills encodé directement: ["MySQL","JavaScript","Docker","CI\/CD","PHP","PostgreSQL","Spring"]  
[2025-07-10 18:53:52] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Anglais"] (type: array)  
[2025-07-10 18:53:52] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand","Anglais"]  
[2025-07-10 18:53:52] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Consulting"] (type: array)  
[2025-07-10 18:53:52] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Consulting"]  
[2025-07-10 18:53:54] local.INFO: setSkillsAttribute appelé avec: ["Azure","PostgreSQL","Docker","CI\/CD","JavaScript"] (type: array)  
[2025-07-10 18:53:54] local.INFO: Skills encodé directement: ["Azure","PostgreSQL","Docker","CI\/CD","JavaScript"]  
[2025-07-10 18:53:54] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: array)  
[2025-07-10 18:53:54] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Anglais","Allemand"]  
[2025-07-10 18:53:54] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-10 18:53:54] local.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-10 18:53:54] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: string)  
[2025-07-10 18:53:54] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Anglais","Allemand"] (type: string)  
[2025-07-10 18:53:54] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:54] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance"] (type: string)  
[2025-07-10 18:53:57] local.INFO: setSkillsAttribute appelé avec: ["PostgreSQL","JavaScript","Scrum","Redis","AWS","Spring","Java"] (type: array)  
[2025-07-10 18:53:57] local.INFO: Skills encodé directement: ["PostgreSQL","JavaScript","Scrum","Redis","AWS","Spring","Java"]  
[2025-07-10 18:53:57] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-10 18:53:57] local.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-10 18:53:57] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting"] (type: array)  
[2025-07-10 18:53:57] local.INFO: Services_offered encodé directement: ["Consulting"]  
[2025-07-10 18:53:58] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: string)  
[2025-07-10 18:53:58] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: string)  
[2025-07-10 18:53:58] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting"] (type: string)  
[2025-07-10 18:53:58] local.INFO: getServicesOfferedAttribute appelé avec: ["Consulting"] (type: string)  
[2025-07-10 18:54:00] local.INFO: setSkillsAttribute appelé avec: ["MongoDB","Azure","Spring"] (type: array)  
[2025-07-10 18:54:00] local.INFO: Skills encodé directement: ["MongoDB","Azure","Spring"]  
[2025-07-10 18:54:00] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Allemand"] (type: array)  
[2025-07-10 18:54:00] local.INFO: Languages encodé directement: ["Anglais","Allemand"]  
[2025-07-10 18:54:00] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: array)  
[2025-07-10 18:54:00] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Maintenance"]  
[2025-07-10 18:54:00] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand"] (type: string)  
[2025-07-10 18:54:00] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand"] (type: string)  
[2025-07-10 18:54:00] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: string)  
[2025-07-10 18:54:00] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Maintenance"] (type: string)  
[2025-07-10 18:54:02] local.INFO: setSkillsAttribute appelé avec: ["CI\/CD","PHP","Kubernetes","Vue.js"] (type: array)  
[2025-07-10 18:54:02] local.INFO: Skills encodé directement: ["CI\/CD","PHP","Kubernetes","Vue.js"]  
[2025-07-10 18:54:02] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Allemand","Espagnol"] (type: array)  
[2025-07-10 18:54:02] local.INFO: Languages encodé directement: ["Anglais","Allemand","Espagnol"]  
[2025-07-10 18:54:02] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation"] (type: array)  
[2025-07-10 18:54:02] local.INFO: Services_offered encodé directement: ["Formation"]  
[2025-07-10 18:54:02] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Espagnol"] (type: string)  
[2025-07-10 18:54:02] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Allemand","Espagnol"] (type: string)  
[2025-07-10 18:54:02] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation"] (type: string)  
[2025-07-10 18:54:02] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation"] (type: string)  
[2025-07-10 18:54:04] local.INFO: setSkillsAttribute appelé avec: ["Django","JavaScript","C#","Azure","Python","Agile","Git","Laravel"] (type: array)  
[2025-07-10 18:54:04] local.INFO: Skills encodé directement: ["Django","JavaScript","C#","Azure","Python","Agile","Git","Laravel"]  
[2025-07-10 18:54:04] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-10 18:54:04] local.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-10 18:54:04] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:54:04] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-10 18:54:04] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:54:04] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:54:04] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:04] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:06] local.INFO: setSkillsAttribute appelé avec: ["C#","Kubernetes","Agile","PHP","AWS","Python"] (type: array)  
[2025-07-10 18:54:06] local.INFO: Skills encodé directement: ["C#","Kubernetes","Agile","PHP","AWS","Python"]  
[2025-07-10 18:54:06] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-10 18:54:06] local.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-10 18:54:06] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:54:06] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-10 18:54:08] local.INFO: setSkillsAttribute appelé avec: ["MySQL","Java","Node.js","Scrum","React","Redis","C#"] (type: array)  
[2025-07-10 18:54:08] local.INFO: Skills encodé directement: ["MySQL","Java","Node.js","Scrum","React","Redis","C#"]  
[2025-07-10 18:54:08] local.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-10 18:54:08] local.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-10 18:54:08] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:54:08] local.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web"]  
[2025-07-10 18:54:08] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:54:08] local.INFO: getLanguagesAttribute appelé avec: ["Espagnol"] (type: string)  
[2025-07-10 18:54:08] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:08] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:13] local.INFO: setSkillsAttribute appelé avec: ["Git","MySQL","MongoDB","Laravel","Docker","Python","JavaScript"] (type: array)  
[2025-07-10 18:54:13] local.INFO: Skills encodé directement: ["Git","MySQL","MongoDB","Laravel","Docker","Python","JavaScript"]  
[2025-07-10 18:54:13] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-10 18:54:13] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol"]  
[2025-07-10 18:54:13] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:54:13] local.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Mobile"]  
[2025-07-10 18:54:16] local.INFO: setSkillsAttribute appelé avec: ["JavaScript","AWS","React","MongoDB","Laravel"] (type: array)  
[2025-07-10 18:54:16] local.INFO: Skills encodé directement: ["JavaScript","AWS","React","MongoDB","Laravel"]  
[2025-07-10 18:54:16] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Anglais"] (type: array)  
[2025-07-10 18:54:16] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand","Anglais"]  
[2025-07-10 18:54:16] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:54:16] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-10 18:54:16] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Anglais"] (type: string)  
[2025-07-10 18:54:16] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand","Anglais"] (type: string)  
[2025-07-10 18:54:16] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:16] local.INFO: getServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: string)  
[2025-07-10 18:54:17] local.INFO: setSkillsAttribute appelé avec: [".NET","Node.js","React","Java","Git"] (type: array)  
[2025-07-10 18:54:17] local.INFO: Skills encodé directement: [".NET","Node.js","React","Java","Git"]  
[2025-07-10 18:54:17] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Allemand"] (type: array)  
[2025-07-10 18:54:17] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Allemand"]  
[2025-07-10 18:54:17] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:54:17] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","D\u00e9veloppement Web"]  
[2025-07-10 18:54:19] local.INFO: setSkillsAttribute appelé avec: ["PostgreSQL",".NET","Spring"] (type: array)  
[2025-07-10 18:54:19] local.INFO: Skills encodé directement: ["PostgreSQL",".NET","Spring"]  
[2025-07-10 18:54:19] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-10 18:54:19] local.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais"]  
[2025-07-10 18:54:19] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile","Maintenance","Consulting"] (type: array)  
[2025-07-10 18:54:19] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile","Maintenance","Consulting"]  
[2025-07-10 18:54:22] local.INFO: setSkillsAttribute appelé avec: ["Django","CI\/CD","MySQL","Docker","C#","Redis","Spring"] (type: array)  
[2025-07-10 18:54:22] local.INFO: Skills encodé directement: ["Django","CI\/CD","MySQL","Docker","C#","Redis","Spring"]  
[2025-07-10 18:54:22] local.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-10 18:54:22] local.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-10 18:54:22] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","Consulting","Maintenance"] (type: array)  
[2025-07-10 18:54:22] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","Consulting","Maintenance"]  
[2025-07-10 18:54:24] local.INFO: setSkillsAttribute appelé avec: ["PostgreSQL","AWS","Docker"] (type: array)  
[2025-07-10 18:54:24] local.INFO: Skills encodé directement: ["PostgreSQL","AWS","Docker"]  
[2025-07-10 18:54:24] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-10 18:54:24] local.INFO: Languages encodé directement: ["Anglais","Fran\u00e7ais","Espagnol"]  
[2025-07-10 18:54:24] local.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:54:24] local.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Mobile"]  
[2025-07-10 18:54:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais","Espagnol"] (type: string)  
[2025-07-10 18:54:24] local.INFO: getLanguagesAttribute appelé avec: ["Anglais","Fran\u00e7ais","Espagnol"] (type: string)  
[2025-07-10 18:54:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:54:24] local.INFO: getServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Mobile"] (type: string)  
[2025-07-10 18:54:26] local.INFO: setSkillsAttribute appelé avec: ["CI\/CD","AWS","C#","Node.js"] (type: array)  
[2025-07-10 18:54:26] local.INFO: Skills encodé directement: ["CI\/CD","AWS","C#","Node.js"]  
[2025-07-10 18:54:26] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol"] (type: array)  
[2025-07-10 18:54:26] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol"]  
[2025-07-10 18:54:26] local.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","D\u00e9veloppement Web"] (type: array)  
[2025-07-10 18:54:26] local.INFO: Services_offered encodé directement: ["Consulting","D\u00e9veloppement Web"]  
[2025-07-10 18:54:30] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","Vue.js","JavaScript"] (type: array)  
[2025-07-10 18:54:30] local.INFO: Skills encodé directement: ["PHP","Laravel","Vue.js","JavaScript"]  
[2025-07-10 18:54:31] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:31] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:35] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:35] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:36] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:36] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:36] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:36] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:38] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:38] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-10 18:54:40] local.INFO: setSkillsAttribute appelé avec: ["Django","PostgreSQL",".NET"] (type: array)  
[2025-07-10 18:54:40] local.INFO: Skills encodé directement: ["Django","PostgreSQL",".NET"]  
[2025-07-10 18:54:40] local.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol"] (type: array)  
[2025-07-10 18:54:40] local.INFO: Languages encodé directement: ["Anglais","Espagnol"]  
[2025-07-10 18:54:40] local.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Mobile"] (type: array)  
[2025-07-10 18:54:40] local.INFO: Services_offered encodé directement: ["D\u00e9veloppement Mobile"]  
[2025-07-10 18:54:41] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:41] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:42] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:42] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:42] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (2, 2, 50, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (2, 50) returning "id")  
[2025-07-10 18:54:43] local.INFO: setSkillsAttribute appelé avec: ["Kubernetes","C#","Python","Node.js","React","Redis","Docker","PostgreSQL"] (type: array)  
[2025-07-10 18:54:43] local.INFO: Skills encodé directement: ["Kubernetes","C#","Python","Node.js","React","Redis","Docker","PostgreSQL"]  
[2025-07-10 18:54:43] local.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: array)  
[2025-07-10 18:54:43] local.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol","Anglais"]  
[2025-07-10 18:54:43] local.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Maintenance"] (type: array)  
[2025-07-10 18:54:43] local.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Mobile","Maintenance"]  
[2025-07-10 18:54:43] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: string)  
[2025-07-10 18:54:43] local.INFO: getLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: string)  
[2025-07-10 18:54:43] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Maintenance"] (type: string)  
[2025-07-10 18:54:43] local.INFO: getServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Maintenance"] (type: string)  
[2025-07-10 18:54:44] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:44] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:45] local.INFO: Tentative de connexion pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:45] local.INFO: Connexion réussie pour l'utilisateur: <EMAIL>  
[2025-07-10 18:54:45] local.ERROR: Matching error: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « sent_at » dans la relation « offer_email_logs »
DETAIL:  La ligne en échec contient (3, 3, 52, null). (Connection: pgsql, SQL: insert into "offer_email_logs" ("offer_id", "user_id") values (3, 52) returning "id")  
[2025-07-11 14:05:12] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","Vue.js","MySQL"] (type: array)  
[2025-07-11 14:05:12] local.INFO: Skills encodé directement: ["PHP","Laravel","React","Vue.js","MySQL"]  
[2025-07-11 14:05:12] local.INFO: setLanguagesAttribute appelé avec: ["French","English"] (type: array)  
[2025-07-11 14:05:12] local.INFO: Languages encodé directement: ["French","English"]  
[2025-07-11 14:05:15] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-11 14:05:15] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-11 14:05:15] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-11 14:05:15] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-11 14:05:18] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-11 14:05:18] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-11 14:05:18] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-11 14:05:18] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-11 14:05:19] local.ERROR: Too few arguments to function App\Services\GlobalSearchService::__construct(), 0 passed in C:\Users\<USER>\Documents\MD\wassim\04Juiell\hi3d\backend\test_global_search.php on line 133 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Services\\GlobalSearchService::__construct(), 0 passed in C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\test_global_search.php on line 133 and exactly 2 expected at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Services\\GlobalSearchService.php:18)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\test_global_search.php(133): App\\Services\\GlobalSearchService->__construct()
#1 {main}
"} 
[2025-07-15 13:14:21] local.ERROR: SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « first_name » dans la relation « users »
DETAIL:  La ligne en échec contient (54, null, null, <EMAIL>, null, $2y$12$DbRi.lf5AdEttOtz5ziLYutq.ccDqeBflAb7lJ3Zp6J8icVBwbJrK, null, f, f, null, 2025-07-15 13:14:20, 2025-07-15 13:14:20, user, null, f, null). (Connection: pgsql, SQL: insert into "users" ("email", "password", "updated_at", "created_at") values (<EMAIL>, $2y$12$DbRi.lf5AdEttOtz5ziLYutq.ccDqeBflAb7lJ3Zp6J8icVBwbJrK, 2025-07-15 13:14:20, 2025-07-15 13:14:20) returning "id") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23502): SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « first_name » dans la relation « users »
DETAIL:  La ligne en échec contient (54, null, null, <EMAIL>, null, $2y$12$DbRi.lf5AdEttOtz5ziLYutq.ccDqeBflAb7lJ3Zp6J8icVBwbJrK, null, f, f, null, 2025-07-15 13:14:20, 2025-07-15 13:14:20, user, null, f, null). (Connection: pgsql, SQL: insert into \"users\" (\"email\", \"password\", \"updated_at\", \"created_at\") values (<EMAIL>, $2y$12$DbRi.lf5AdEttOtz5ziLYutq.ccDqeBflAb7lJ3Zp6J8icVBwbJrK, 2025-07-15 13:14:20, 2025-07-15 13:14:20) returning \"id\") at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('insert into \"us...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"us...', Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\User), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(44): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(76): Filament\\Commands\\MakeUserCommand->createUser()
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}

[previous exception] [object] (PDOException(code: 23502): SQLSTATE[23502]: Not null violation: 7 ERREUR:  une valeur NULL viole la contrainte NOT NULL de la colonne « first_name » dans la relation « users »
DETAIL:  La ligne en échec contient (54, null, null, <EMAIL>, null, $2y$12$DbRi.lf5AdEttOtz5ziLYutq.ccDqeBflAb7lJ3Zp6J8icVBwbJrK, null, f, f, null, 2025-07-15 13:14:20, 2025-07-15 13:14:20, user, null, f, null). at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"us...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('insert into \"us...', Array, false)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"us...', Array)
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\User), Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(44): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(76): Filament\\Commands\\MakeUserCommand->createUser()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-15 13:47:56] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(570): route('login')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}
"} 
[2025-07-15 14:08:13] local.ERROR: Method Filament\Forms\Components\TextInput::revealable does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Filament\\Forms\\Components\\TextInput::revealable does not exist. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\Macroable.php:72)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Pages\\MeilisearchManagement.php(60): Filament\\Support\\Components\\ViewComponent->__call('revealable', Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(287): App\\Filament\\Pages\\MeilisearchManagement->form(Object(Filament\\Forms\\Form))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(627): App\\Filament\\Pages\\MeilisearchManagement->Filament\\Forms\\Concerns\\{closure}('form', 0)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(826): Illuminate\\Support\\Arr::mapWithKeys(Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(272): Illuminate\\Support\\Collection->mapWithKeys(Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(341): App\\Filament\\Pages\\MeilisearchManagement->cacheForms()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(332): App\\Filament\\Pages\\MeilisearchManagement->getCachedForms()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(27): App\\Filament\\Pages\\MeilisearchManagement->getForm('form')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(20): Filament\\Pages\\BasePage->__get('form')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Pages\\MeilisearchManagement.php(36): App\\Filament\\Pages\\MeilisearchManagement->__get('form')
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\MeilisearchManagement->mount()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\MeilisearchManagement), '__invoke')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#82 {main}
"} 
[2025-07-15 14:08:57] local.ERROR: Method Filament\Forms\Components\TextInput::revealable does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Filament\\Forms\\Components\\TextInput::revealable does not exist. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\Macroable.php:72)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Pages\\MeilisearchManagement.php(60): Filament\\Support\\Components\\ViewComponent->__call('revealable', Array)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(287): App\\Filament\\Pages\\MeilisearchManagement->form(Object(Filament\\Forms\\Form))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(627): App\\Filament\\Pages\\MeilisearchManagement->Filament\\Forms\\Concerns\\{closure}('form', 0)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(826): Illuminate\\Support\\Arr::mapWithKeys(Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(272): Illuminate\\Support\\Collection->mapWithKeys(Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(341): App\\Filament\\Pages\\MeilisearchManagement->cacheForms()
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php(332): App\\Filament\\Pages\\MeilisearchManagement->getCachedForms()
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(27): App\\Filament\\Pages\\MeilisearchManagement->getForm('form')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\support\\src\\Concerns\\ResolvesDynamicLivewireProperties.php(20): Filament\\Pages\\BasePage->__get('form')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Filament\\Pages\\MeilisearchManagement.php(36): App\\Filament\\Pages\\MeilisearchManagement->__get('form')
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\MeilisearchManagement->mount()
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::Livewire\\{closure}(Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\MeilisearchManagement), Array, NULL, false)
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->Livewire\\Features\\SupportPageComponents\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\MeilisearchManagement), '__invoke')
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#54 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#58 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#82 {main}
"} 
[2025-07-21 13:40:53] local.ERROR: SQLSTATE[42P07]: Duplicate table: 7 ERREUR:  la relation « files_fileable_type_fileable_id_index » existe déjà (Connection: pgsql, SQL: create index "files_fileable_type_fileable_id_index" on "files" ("fileable_type", "fileable_id")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERREUR:  la relation « files_fileable_type_fileable_id_index » existe déjà (Connection: pgsql, SQL: create index \"files_fileable_type_fileable_id_index\" on \"files\" (\"fileable_type\", \"fileable_id\")) at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create index \"f...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create index \"f...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create index \"f...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('files', Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2025_07_21_133239_create_files_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_07_21_1332...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_21_1332...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: 42P07): SQLSTATE[42P07]: Duplicate table: 7 ERREUR:  la relation « files_fileable_type_fileable_id_index » existe déjà at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create index \"f...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create index \"f...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create index \"f...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create index \"f...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('files', Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2025_07_21_133239_create_files_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_07_21_1332...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_21_1332...', Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-07-21 14:00:21] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'files')
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=files')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=files', true)
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
