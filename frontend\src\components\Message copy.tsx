import Header from './Header';
import Footer from './Footer';
import Container from './layout/Container';
import React, { useState } from 'react';
import MessageSidebar from './MessageSidebar';
import MessageContent from './MessageContent';
import MessageProfile from './MessageProfile';
import Brief from './Brief';
import Attachement from './Attachement';

const Message = () => {
  const [activeTab, setActiveTab] = useState<'messages' | 'brief' | 'attachments'>('messages');
  return (
    <>
      <Header />
      <main style={{ minHeight: '60vh' }}>
        <Container padding={false}>
          <h2 style={{ fontSize: '2rem', fontWeight: '600', margin: '65px 0 0 0' }}>Project Name</h2>
          <div style={{ display: 'flex', gap: 32, borderBottom: '1.5px solid #222', width: '100%', maxWidth: 1200, marginTop: 24 }}>
            {[
              { key: 'messages', label: 'Messages' },
              { key: 'brief', label: 'Brief' },
              { key: 'attachments', label: 'Attachments' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as 'messages' | 'brief' | 'attachments')}
                style={{
                  background: 'none',
                  border: 'none',
                  borderBottom: activeTab === tab.key ? '2.5px solid #222' : 'none',
                  color: activeTab === tab.key ? '#222' : '#888',
                  fontWeight: activeTab === tab.key ? 600 : 400,
                  fontSize: 15,
                  padding: '12px 0',
                  cursor: 'pointer',
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
          <div style={{ display: 'flex', width: '100%', maxWidth: 1600, margin: '40px auto 0 auto', background: '#fff', borderRadius: 24 }}>
            {activeTab === 'brief' ? (
              <Brief />
            ) : activeTab === 'attachments' ? (
              <Attachement />
            ) : (
              <>
                <MessageSidebar />
                <MessageContent />
                <MessageProfile />
              </>
            )}
          </div>
        </Container>
      </main>
      <Footer />
    </>
  );
};

export default Message; 